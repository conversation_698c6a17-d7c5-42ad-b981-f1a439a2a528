#!/usr/bin/env python3

import numpy as np
import os

def debug_llff_data():
    """调试LLFF数据加载"""
    
    scene_path = "datasets/LLFF/scene"
    poses_bounds_file = os.path.join(scene_path, 'poses_bounds.npy')
    
    if not os.path.exists(poses_bounds_file):
        print(f"poses_bounds.npy not found at {poses_bounds_file}")
        return
    
    # 加载poses_bounds
    poses_arr = np.load(poses_bounds_file)
    poses = poses_arr[:, :-2].reshape([-1, 3, 5])  # (N_cams, 3, 5)
    bounds = poses_arr[:, -2:]  # (N_cams, 2)
    
    print(f"总相机数: {len(poses)}")
    print(f"poses shape: {poses.shape}")
    
    # 检查每个相机的内参
    print("\n=== 各相机内参 ===")
    for i in range(min(len(poses), 5)):  # 只显示前5个
        H, W, focal = poses[i, :, -1]
        print(f"相机 {i}: H={H:.1f}, W={W:.1f}, focal={focal:.2f}")
    
    # 检查LLFF的训练/测试分割 (通常每8个取1个作为测试)
    llffhold = 8
    test_indices = list(range(0, len(poses), llffhold))
    train_indices = [i for i in range(len(poses)) if i not in test_indices]
    
    print(f"\n=== 训练/测试分割 (llffhold={llffhold}) ===")
    print(f"训练相机索引: {train_indices}")
    print(f"测试相机索引: {test_indices}")
    
    # 检查训练和测试相机的内参是否一致
    if test_indices and train_indices:
        train_params = poses[train_indices[0], :, -1]  # 第一个训练相机
        test_params = poses[test_indices[0], :, -1]    # 第一个测试相机
        
        print(f"\n=== 参数对比 ===")
        print(f"训练相机 {train_indices[0]}: H={train_params[0]:.1f}, W={train_params[1]:.1f}, focal={train_params[2]:.2f}")
        print(f"测试相机 {test_indices[0]}: H={test_params[0]:.1f}, W={test_params[1]:.1f}, focal={test_params[2]:.2f}")
        
        # 检查是否有差异
        if not np.allclose(train_params, test_params, rtol=1e-3):
            print("⚠️  警告: 训练和测试相机内参不一致!")
            print(f"差异: ΔH={test_params[0]-train_params[0]:.2f}, ΔW={test_params[1]-train_params[1]:.2f}, Δfocal={test_params[2]-train_params[2]:.2f}")

if __name__ == "__main__":
    debug_llff_data()