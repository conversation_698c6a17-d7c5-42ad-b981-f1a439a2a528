#!/usr/bin/env python3

import sys
sys.path.append('.')

import torch
from scene import Scene
from scene.gaussian_model import GaussianModel
from arguments import ModelParams, PipelineParams
from argparse import ArgumentParser

def check_test_cameras():
    """检查测试相机配置"""
    
    parser = ArgumentParser()
    lp = ModelParams(parser)
    pp = PipelineParams(parser)
    
    args = parser.parse_args([
        "-s", "datasets/LLFF/scene",
        "--images", "images",
        "--depth_method", "monster"
    ])
    
    model_params = lp.extract(args)
    
    gaussians = GaussianModel(model_params)
    scene = Scene(model_params, gaussians)
    
    train_cams = scene.getTrainCameras()
    test_cams = scene.getTestCameras()
    
    print("=== 相机配置对比 ===")
    print(f"训练相机数: {len(train_cams)}")
    print(f"测试相机数: {len(test_cams)}")
    
    if train_cams:
        train_cam = train_cams[0]
        print(f"\n训练相机示例:")
        print(f"  尺寸: {train_cam.image_width}×{train_cam.image_height}")
        print(f"  tensor: {train_cam.original_image.shape}")
        print(f"  有深度: {hasattr(train_cam, 'monster_depth') and train_cam.monster_depth is not None}")
    
    if test_cams:
        for i, test_cam in enumerate(test_cams):
            print(f"\n测试相机 {i} ({test_cam.image_name}):")
            print(f"  尺寸: {test_cam.image_width}×{test_cam.image_height}")
            print(f"  tensor: {test_cam.original_image.shape}")
            print(f"  有深度: {hasattr(test_cam, 'monster_depth') and test_cam.monster_depth is not None}")
            
            # 检查尺寸比例
            if train_cams:
                width_ratio = test_cam.image_width / train_cam.image_width
                height_ratio = test_cam.image_height / train_cam.image_height
                print(f"  与训练相机比例: {width_ratio:.3f}×{height_ratio:.3f}")

if __name__ == "__main__":
    check_test_cameras()