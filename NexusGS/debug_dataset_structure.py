#!/usr/bin/env python3

import sys
sys.path.append('.')

import os
import numpy as np
from PIL import Image

def debug_dataset_structure():
    """调试数据集结构"""
    
    dataset_path = "datasets/LLFF/scene"
    
    print("=== 数据集结构调试 ===")
    print(f"数据集路径: {dataset_path}")
    
    # 检查基本文件结构
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return
    
    print(f"✅ 数据集路径存在")
    
    # 检查poses_bounds.npy
    poses_bounds_file = os.path.join(dataset_path, 'poses_bounds.npy')
    if os.path.exists(poses_bounds_file):
        poses_arr = np.load(poses_bounds_file)
        print(f"✅ poses_bounds.npy 存在, shape: {poses_arr.shape}")
        
        poses = poses_arr[:, :-2].reshape([-1, 3, 5])
        bounds = poses_arr[:, -2:]
        print(f"   相机数量: {len(poses)}")
        print(f"   poses shape: {poses.shape}")
        print(f"   bounds shape: {bounds.shape}")
    else:
        print(f"❌ poses_bounds.npy 不存在")
    
    # 检查图像文件夹
    images_folders = ['images', 'images_4', 'images_8']
    for folder in images_folders:
        folder_path = os.path.join(dataset_path, folder)
        if os.path.exists(folder_path):
            image_files = [f for f in os.listdir(folder_path) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            print(f"✅ {folder} 存在, 包含 {len(image_files)} 个图像文件")
            
            if image_files:
                # 检查第一个图像的尺寸
                first_img_path = os.path.join(folder_path, image_files[0])
                try:
                    img = Image.open(first_img_path)
                    print(f"   第一个图像尺寸: {img.size} (W×H)")
                except Exception as e:
                    print(f"   ❌ 无法读取图像: {e}")
        else:
            print(f"❌ {folder} 不存在")
    
    # 检查COLMAP文件
    colmap_files = ['cameras.txt', 'images.txt', 'points3D.txt']
    sparse_path = os.path.join(dataset_path, 'sparse', '0')
    if os.path.exists(sparse_path):
        print(f"✅ sparse/0 目录存在")
        for file in colmap_files:
            file_path = os.path.join(sparse_path, file)
            if os.path.exists(file_path):
                print(f"   ✅ {file} 存在")
            else:
                print(f"   ❌ {file} 不存在")
    else:
        print(f"❌ sparse/0 目录不存在")

if __name__ == "__main__":
    debug_dataset_structure()