#!/usr/bin/env python3

import sys
sys.path.append('.')

import os
import numpy as np
from scene.dataset_readers import readColmapSceneInfo

def debug_camera_loading_simple():
    """简化的相机加载调试"""
    
    source_path = "datasets/LLFF/scene"
    images = "images"  # 使用存在的images文件夹
    
    print("=== 简化相机加载调试 ===")
    print(f"源路径: {source_path}")
    print(f"图像文件夹: {images}")
    
    try:
        # 尝试读取场景信息
        print("正在读取场景信息...")
        scene_info = readColmapSceneInfo(source_path, images, eval=False, llffhold=8)
        
        print(f"✅ 场景信息读取成功")
        print(f"训练相机数: {len(scene_info.train_cameras)}")
        print(f"测试相机数: {len(scene_info.test_cameras)}")
        
        # 检查训练相机
        if scene_info.train_cameras:
            print(f"\n=== 训练相机信息 ===")
            for i, cam_info in enumerate(scene_info.train_cameras[:3]):
                print(f"训练相机 {i}:")
                print(f"  image_name: {cam_info.image_name}")
                if hasattr(cam_info, 'image'):
                    print(f"  image size: {cam_info.image.size}")
                print(f"  FovX: {cam_info.FovX:.6f}")
                print(f"  FovY: {cam_info.FovY:.6f}")
        
        # 检查测试相机
        if scene_info.test_cameras:
            print(f"\n=== 测试相机信息 ===")
            for i, cam_info in enumerate(scene_info.test_cameras):
                print(f"测试相机 {i}:")
                print(f"  image_name: {cam_info.image_name}")
                if hasattr(cam_info, 'image'):
                    print(f"  image size: {cam_info.image.size}")
                print(f"  FovX: {cam_info.FovX:.6f}")
                print(f"  FovY: {cam_info.FovY:.6f}")
        
    except Exception as e:
        print(f"❌ 读取场景信息失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_camera_loading_simple()
