#!/usr/bin/env python3

import sys
sys.path.append('.')

import torch
import numpy as np
from scene import Scene
from scene.gaussian_model import GaussianModel
from arguments import ModelParams, PipelineParams, OptimizationParams
from argparse import ArgumentParser
import math

def compare_train_test_cameras():
    """对比训练和测试相机参数"""
    
    # 正确的参数解析方式
    parser = ArgumentParser(description="Compare train/test cameras")
    lp = ModelParams(parser)
    pp = PipelineParams(parser)
    op = OptimizationParams(parser)
    
    args = parser.parse_args([
        "-s", "datasets/LLFF/scene",
        "--depth_method", "monster"
    ])
    
    # 提取参数对象
    model_params = lp.extract(args)
    
    # 正确传递args对象给GaussianModel
    gaussians = GaussianModel(model_params)
    scene = Scene(model_params, gaussians)
    
    train_cams = scene.getTrainCameras()
    test_cams = scene.getTestCameras()
    
    print("=== 训练 vs 测试相机对比 ===")
    
    # 检查第一个训练相机
    if train_cams:
        train_cam = train_cams[0]
        print(f"训练相机 0:")
        print(f"  尺寸: {train_cam.image_width}x{train_cam.image_height}")
        print(f"  FovX: {train_cam.FoVx:.6f} ({math.degrees(train_cam.FoVx):.2f}°)")
        print(f"  FovY: {train_cam.FoVy:.6f} ({math.degrees(train_cam.FoVy):.2f}°)")
        
        # 计算等效焦距
        fx_train = train_cam.image_width / (2 * math.tan(train_cam.FoVx / 2))
        fy_train = train_cam.image_height / (2 * math.tan(train_cam.FoVy / 2))
        print(f"  等效焦距: fx={fx_train:.2f}, fy={fy_train:.2f}")
    
    print()
    
    # 检查第一个测试相机
    if test_cams:
        test_cam = test_cams[0]
        print(f"测试相机 0:")
        print(f"  尺寸: {test_cam.image_width}x{test_cam.image_height}")
        print(f"  FovX: {test_cam.FoVx:.6f} ({math.degrees(test_cam.FoVx):.2f}°)")
        print(f"  FovY: {test_cam.FoVy:.6f} ({math.degrees(test_cam.FoVy):.2f}°)")
        
        fx_test = test_cam.image_width / (2 * math.tan(test_cam.FoVx / 2))
        fy_test = test_cam.image_height / (2 * math.tan(test_cam.FoVy / 2))
        print(f"  等效焦距: fx={fx_test:.2f}, fy={fy_test:.2f}")
        
        # 对比差异
        if train_cams:
            print(f"\n=== 差异分析 ===")
            size_match = (train_cam.image_width == test_cam.image_width and 
                         train_cam.image_height == test_cam.image_height)
            print(f"尺寸匹配: {size_match}")
            
            fov_diff_x = abs(train_cam.FoVx - test_cam.FoVx)
            fov_diff_y = abs(train_cam.FoVy - test_cam.FoVy)
            print(f"FoV差异: X={fov_diff_x:.6f}, Y={fov_diff_y:.6f}")
            
            if fov_diff_x > 0.001 or fov_diff_y > 0.001:
                print("⚠️  警告: 训练和测试相机的视场角差异较大!")
                print(f"焦距比例: fx_ratio={fx_test/fx_train:.4f}, fy_ratio={fy_test/fy_train:.4f}")
            else:
                print("✅ 训练和测试相机参数一致")

if __name__ == "__main__":
    compare_train_test_cameras()