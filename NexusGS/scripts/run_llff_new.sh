export CUDA_VISIBLE_DEVICES=0

name=scene
n=5
dataset=datasets/LLFF/$name
workspace=output/llff/$name  # 修改为正确的输出路径
# 不要使用预训练模型路径作为workspace
iterations=10000
dataset_type=llff
images=images
split_num=4
valid_dis_threshold=1.0
drop_rate=1.0
near_n=2

python train.py --source_path $dataset --model_path $workspace --eval --n_views $n \
    --save_iterations 5000 \
    --iterations $iterations \
    --densify_until_iter $iterations \
    --position_lr_max_steps $iterations \
    --dataset_type $dataset_type \
    --images $images \
    --split_num $split_num \
    --valid_dis_threshold $valid_dis_threshold \
    --drop_rate $drop_rate \
    --near_n $near_n \
    --depth_method monster \
    --resolution -1  # 使用自动分辨率


python render.py --source_path $dataset  --model_path  $workspace --iteration $iterations --render_depth


python metrics.py --source_path $dataset --model_path $workspace 
