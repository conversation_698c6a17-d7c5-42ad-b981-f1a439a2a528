#!/usr/bin/env python3

import sys
sys.path.append('.')

import os
from PIL import Image
from utils.camera_utils import loadCam
from scene.dataset_readers import readColmapSceneInfo
from arguments import ModelParams
from argparse import ArgumentParser

def debug_camera_loading():
    """调试相机加载过程"""
    
    parser = ArgumentParser()
    args = ModelParams(parser).parse_args([
        "-s", "datasets/LLFF/scene",
        "--depth_method", "monster"
    ])
    
    # 读取场景信息
    scene_info = readColmapSceneInfo(args.source_path, args.images, args.eval, llffhold=8)
    
    print("=== 场景信息 ===")
    print(f"训练相机数: {len(scene_info.train_cameras)}")
    print(f"测试相机数: {len(scene_info.test_cameras)}")
    
    # 检查原始相机信息
    print(f"\n=== 原始相机信息 ===")
    if scene_info.train_cameras:
        train_cam_info = scene_info.train_cameras[0]
        print(f"训练相机 0:")
        print(f"  image size: {train_cam_info.image.size}")
        print(f"  image_name: {train_cam_info.image_name}")
    
    if scene_info.test_cameras:
        test_cam_info = scene_info.test_cameras[0]
        print(f"测试相机 0:")
        print(f"  image size: {test_cam_info.image.size}")
        print(f"  image_name: {test_cam_info.image_name}")
    
    # 模拟loadCam过程
    print(f"\n=== 模拟loadCam过程 ===")
    resolution_scale = 1.0
    
    if scene_info.train_cameras:
        print("加载训练相机...")
        train_cam = loadCam(args, 0, scene_info.train_cameras[0], resolution_scale)
        print(f"训练相机最终尺寸: {train_cam.image_width}×{train_cam.image_height}")
    
    if scene_info.test_cameras:
        print("加载测试相机...")
        test_cam = loadCam(args, 0, scene_info.test_cameras[0], resolution_scale)
        print(f"测试相机最终尺寸: {test_cam.image_width}×{test_cam.image_height}")

if __name__ == "__main__":
    debug_camera_loading()