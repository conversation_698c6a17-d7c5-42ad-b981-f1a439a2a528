#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#
import matplotlib.pyplot as plt
import torch
from scene import Scene, HFScene
import os
from tqdm import tqdm
import numpy as np
from os import makedirs
from gaussian_renderer import render
import torchvision
from utils.general_utils import safe_state
from argparse import ArgumentParser
from arguments import ModelParams, PipelineParams, get_combined_args
from gaussian_renderer import GaussianModel
import cv2
import time
from tqdm import tqdm

from utils.graphics_utils import getWorld2View2
from utils.pose_utils import generate_ellipse_path, generate_spiral_path
from utils.general_utils import vis_depth
import matplotlib.cm as cm

def weighted_percentile(x, w, ps, assume_sorted=False):
    """Compute the weighted percentile(s) of a single vector."""
    x = x.reshape([-1])
    w = w.reshape([-1])
    if not assume_sorted:
        sortidx = np.argsort(x)
    x, w = x[sortidx], w[sortidx]
    acc_w = np.cumsum(w)
    return np.interp(np.array(ps) * (acc_w[-1] / 100), acc_w, x)

def visualize_cmap(value,
                   weight,
                   colormap,
                   lo=None,
                   hi=None,
                   percentile=99.,
                   curve_fn=lambda x: x,
                   modulus=None,
                   matte_background=True):
    """Visualize a 1D image and a 1D weighting according to some colormap.

    Args:
    value: A 1D image.
    weight: A weight map, in [0, 1].
    colormap: A colormap function.
    lo: The lower bound to use when rendering, if None then use a percentile.
    hi: The upper bound to use when rendering, if None then use a percentile.
    percentile: What percentile of the value map to crop to when automatically
      generating `lo` and `hi`. Depends on `weight` as well as `value'.
    curve_fn: A curve function that gets applied to `value`, `lo`, and `hi`
      before the rest of visualization. Good choices: x, 1/(x+eps), log(x+eps).
    modulus: If not None, mod the normalized value by `modulus`. Use (0, 1]. If
      `modulus` is not None, `lo`, `hi` and `percentile` will have no effect.
    matte_background: If True, matte the image over a checkerboard.

    Returns:
    A colormap rendering.
    """
    # Identify the values that bound the middle of `value' according to `weight`.
    lo_auto, hi_auto = weighted_percentile(
      value, weight, [50 - percentile / 2, 50 + percentile / 2])

    # If `lo` or `hi` are None, use the automatically-computed bounds above.
    eps = np.finfo(np.float32).eps
    lo = lo or (lo_auto - eps)
    hi = hi or (hi_auto + eps)

    # Curve all values.
    value, lo, hi = [curve_fn(x) for x in [value, lo, hi]]

    # Wrap the values around if requested.
    if modulus:
        value = np.mod(value, modulus) / modulus
    else:
        # Otherwise, just scale to [0, 1].
        value = np.nan_to_num(
        np.clip((value - np.minimum(lo, hi)) / np.abs(hi - lo), 0, 1))

    if colormap:
        colorized = colormap(value)[:, :, :3]
    else:
        assert len(value.shape) == 3 and value.shape[-1] == 3
        colorized = value

    return colorized

depth_curve_fn = lambda x: -np.log(x + np.finfo(np.float32).eps)

def render_set(model_path, name, iteration, views, gaussians, pipeline, background, args):
    render_path = os.path.join(model_path, name, "ours_{}".format(iteration), "renders")
    gts_path = os.path.join(model_path, name, "ours_{}".format(iteration), "gt")
    pc_path = os.path.join(model_path, name, "ours_{}".format(iteration), "pc")

    makedirs(render_path, exist_ok=True)
    makedirs(gts_path, exist_ok=True)
    makedirs(pc_path, exist_ok=True)

    for idx, view in enumerate(tqdm(views, desc="Rendering progress")):
        # 添加详细调试信息
        print(f"\n=== 渲染 {name} 相机 {idx} ({view.image_name}) ===")
        print(f"相机配置尺寸: {view.image_width}×{view.image_height}")
        print(f"原始图像shape: {view.original_image.shape}")
        
        # 检查深度信息
        if hasattr(view, 'monster_depth') and view.monster_depth is not None:
            print(f"MonSter深度shape: {view.monster_depth.shape}")
            print(f"深度值范围: {view.monster_depth.min():.4f} ~ {view.monster_depth.max():.4f}")
        else:
            print("⚠️  没有MonSter深度信息")
            
        if hasattr(view, 'flow_depth') and view.flow_depth is not None:
            print(f"Flow深度shape: {view.flow_depth.shape}")
        else:
            print("⚠️  没有Flow深度信息")
        
        rendering = render(view, gaussians, pipeline, background)
        print(f"渲染结果shape: {rendering['render'].shape}")
        
        gt = view.original_image[0:3, :, :]
        print(f"GT图像shape: {gt.shape}")
        
        # 检查尺寸一致性
        if rendering["render"].shape != gt.shape:
            print(f"❌ 尺寸不匹配! 渲染:{rendering['render'].shape} vs GT:{gt.shape}")
            height_ratio = rendering["render"].shape[1] / gt.shape[1]
            width_ratio = rendering["render"].shape[2] / gt.shape[2]
            print(f"   尺寸比例: H={height_ratio:.4f}, W={width_ratio:.4f}")
        else:
            print("✅ 渲染和GT尺寸匹配")
        
        torchvision.utils.save_image(rendering["render"], os.path.join(render_path, view.image_name + '.png'))
        torchvision.utils.save_image(gt, os.path.join(gts_path, view.image_name + ".png"))

        if args.render_depth:
            print(f"渲染深度shape: {rendering['depth'].shape}")
            depth = (rendering['depth'] - rendering['depth'].min()) / (rendering['depth'].max() - rendering['depth'].min()) + 1 * (1 - rendering["alpha"])
            depth_est = depth.squeeze().cpu().numpy()
            depth_map = visualize_cmap(depth_est, np.ones_like(depth_est), cm.get_cmap('turbo'), curve_fn=depth_curve_fn).copy()
            np.save(os.path.join(render_path, view.image_name + '_depth.npy'), rendering['depth'][0].detach().cpu().numpy())
            depth_map = torch.as_tensor(depth_map).permute(2,0,1)
            torchvision.utils.save_image(depth_map, os.path.join(render_path, view.image_name + '_depth.png'))



def render_video(source_path, model_path, iteration, views, gaussians, pipeline, background, fps=30):
    render_path = os.path.join(model_path, 'video', "ours_{}".format(iteration))
    makedirs(render_path, exist_ok=True)
    view = views[0]

    if source_path.find('dtu') != -1:
        render_poses = generate_spiral_path(np.load(source_path + '/poses_bounds.npy'))

    elif source_path.find('llff') != -1:
        render_poses = generate_spiral_path(np.load(source_path + '/poses_bounds.npy'))
    elif source_path.find('360') != -1:
        render_poses = generate_ellipse_path(views)

    size = (view.original_image.shape[2], view.original_image.shape[1])
    fourcc = cv2.VideoWriter_fourcc(*'XVID')
    final_video = cv2.VideoWriter(os.path.join(render_path, 'final_video.mp4'), fourcc, fps, size)
    final_video_depth = cv2.VideoWriter(os.path.join(render_path, 'final_video_depth.mp4'), fourcc, fps, size)
    # final_video = cv2.VideoWriter(os.path.join('/ssd1/zehao/gs_release/video/', str(iteration), model_path.split('/')[-1] + '.mp4'), fourcc, fps, size)

    for idx, pose in enumerate(tqdm(render_poses, desc="Rendering progress")):
        view.world_view_transform = torch.tensor(getWorld2View2(pose[:3, :3].T, pose[:3, 3], view.trans, view.scale)).transpose(0, 1).cuda()
        view.full_proj_transform = (view.world_view_transform.unsqueeze(0).bmm(view.projection_matrix.unsqueeze(0))).squeeze(0)
        view.camera_center = view.world_view_transform.inverse()[3, :3]
        rendering = render(view, gaussians, pipeline, background)

        img = torch.clamp(rendering["render"], min=0., max=1.)
        torchvision.utils.save_image(img, os.path.join(render_path, '{0:05d}'.format(idx) + ".png"))
        video_img = (img.permute(1, 2, 0).detach().cpu().numpy() * 255.).astype(np.uint8)[..., ::-1]
        final_video.write(video_img)

        if args.render_depth:
            depth = 1.0 - (rendering['depth'] - rendering['depth'].min()) / (rendering['depth'].max() - rendering['depth'].min())
            depth_est = (1 - depth * rendering["alpha"]).squeeze().cpu().numpy()
            depth_map = visualize_cmap(depth_est, np.ones_like(depth_est), cm.get_cmap('turbo'), curve_fn=depth_curve_fn).copy()
            # depth_map = vis_depth(rendering['depth'][0].detach().cpu().numpy())
            np.save(os.path.join(render_path, view.image_name + '_depth.npy'), rendering['depth'][0].detach().cpu().numpy())
            # cv2.imwrite(os.path.join(render_path, view.image_name + '_depth.png'), depth_map)
            depth_map = torch.as_tensor(depth_map).permute(2,0,1)
            # torchvision.utils.save_image(depth_map, os.path.join(render_path, view.image_name + '_depth.png'))
            video_img = (depth_map.permute(1, 2, 0).detach().cpu().numpy() * 255.).astype(np.uint8)[..., ::-1]
            final_video_depth.write(video_img)

    final_video.release()
    final_video_depth.release()


def render_sets(dataset : ModelParams, pipeline : PipelineParams, args):
    gaussians = GaussianModel(args)
    if args.huggingface:
        scene = HFScene(args, gaussians, load_iteration=args.iteration, shuffle=False)
    else:
        scene = Scene(args, gaussians, load_iteration=args.iteration, shuffle=False)
    with torch.no_grad():
        #gaussians = GaussianModel(args)
        #scene = Scene(args, gaussians, load_iteration=args.iteration, shuffle=False)

        bg_color = [1,1,1] if dataset.white_background else [0, 0, 0]
        background = torch.tensor(bg_color, dtype=torch.float32, device="cuda")

        if args.video:
            render_video(dataset.source_path, dataset.model_path, scene.loaded_iter, scene.getTestCameras(),
                         gaussians, pipeline, background, args.fps)

        if not args.skip_train:
            render_set(dataset.model_path, "train", scene.loaded_iter, scene.getTrainCameras(), gaussians, pipeline, background, args)
        if not args.skip_test:
            render_set(dataset.model_path, "test", scene.loaded_iter, scene.getTestCameras(), gaussians, pipeline, background, args)
        if args.render_eval:
            render_set(dataset.model_path, "eval", scene.loaded_iter, scene.getEvalCameras(), gaussians, pipeline, background, args)



if __name__ == "__main__":
    # Set up command line argument parser
    parser = ArgumentParser(description="Testing script parameters")
    model = ModelParams(parser, sentinel=True)
    pipeline = PipelineParams(parser)
    parser.add_argument("--iteration", default=-1, type=int)
    parser.add_argument("--skip_train", action="store_true")
    parser.add_argument("--skip_test", action="store_true")
    parser.add_argument("--render_eval", action="store_true")
    parser.add_argument("--quiet", action="store_true")
    parser.add_argument("--video", action="store_true")
    parser.add_argument("--fps", default=30, type=int)
    parser.add_argument("--render_depth", action="store_true")
    args = get_combined_args(parser)
    print("Rendering " + args.model_path)

    # name = str(args.dataset_type) + '_' + str(args.images) + '_' + str(args.flow_type) + '_' + str(args.flow_checkpoint) + '_scalelr' + str(args.scaling_lr) + '_depth' + str(args.depth_weight) + '_near' + str(args.near_n) + '_valid' + str(args.valid_dis_threshold) + '_drop' + str(args.drop_rate) + '_N' + str(args.split_num)
    
    # args.model_path = os.path.join(args.model_path, name, os.path.split(args.source_path)[-1], str(args.n_views) + '_views')
    # print(args.model_path)

    # Initialize system state (RNG)
    safe_state(args.quiet)

    render_sets(model.extract(args), pipeline.extract(args), args)
