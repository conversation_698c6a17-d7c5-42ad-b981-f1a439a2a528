#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#

import torch
from torch import nn
import numpy as np
import cv2
from utils.graphics_utils import getWorld2View2, getProjectionMatrix

class Camera(nn.Module):
    def __init__(self, colmap_id, R, T, FoVx, FoVy, image, gt_alpha_mask,
                 image_name, uid, trans=np.array([0.0, 0.0, 0.0]), scale=1.0, data_device="cuda",
                 mask=None, bounds=None, flow=None):
        super(Camera, self).__init__()

        self.uid = uid
        self.colmap_id = colmap_id
        self.R = R
        self.T = T
        self.FoVx = FoVx
        self.FoVy = FoVy
        self.image_name = image_name
        self.mask = mask
        self.bounds = bounds
        self.flow = flow

        try:
            self.data_device = torch.device(data_device)
        except Exception as e:
            print(e)
            print(f"[Warning] Custom device {data_device} failed, fallback to default cuda device")
            self.data_device = torch.device("cuda")

        self.original_image = image.clamp(0.0, 1.0).to(self.data_device)
        self.image_width = self.original_image.shape[2]
        self.image_height = self.original_image.shape[1]

        if gt_alpha_mask is not None:
            self.original_image *= gt_alpha_mask.to(self.data_device)
        else:
            self.original_image *= torch.ones((1, self.image_height, self.image_width), device=self.data_device)

        self.zfar = 100.0
        self.znear = 0.01

        self.trans = trans
        self.scale = scale

        self.world_view_transform = torch.tensor(getWorld2View2(R, T, trans, scale)).transpose(0, 1).cuda()
        self.projection_matrix = getProjectionMatrix(znear=self.znear, zfar=self.zfar, fovX=self.FoVx, fovY=self.FoVy).transpose(0,1).cuda()
        self.full_proj_transform = (self.world_view_transform.unsqueeze(0).bmm(self.projection_matrix.unsqueeze(0))).squeeze(0)
        self.camera_center = self.world_view_transform.inverse()[3, :3]
        
        # 初始化所有深度相关属性
        self.monster_depth = None
        self.monster_depth_mask = None
        self.monster_confidence = None
        self.flow_depth = None
        self.flow_depth_mask = None

    def set_flow_depth(self, depth, depth_mask):
        """设置光流深度数据"""
        self.flow_depth = depth.to(self.data_device) if depth is not None else None
        self.flow_depth_mask = depth_mask.to(self.data_device) if depth_mask is not None else None

    def set_monster_depth(self, depth, depth_mask, confidence=None):
        """设置MonSter深度数据"""
        self.monster_depth = depth.to(self.data_device) if depth is not None else None
        self.monster_depth_mask = depth_mask.to(self.data_device) if depth_mask is not None else None
        self.monster_confidence = confidence.to(self.data_device) if confidence is not None else None

    def get_effective_depth(self, method="monster"):
        """获取有效深度数据"""
        if method == "monster" and self.monster_depth is not None:
            return self.monster_depth, self.monster_depth_mask
        elif method == "flow" and self.flow_depth is not None:
            return self.flow_depth, self.flow_depth_mask
        elif method == "hybrid":
            return self._get_fused_depth()
        else:
            # 如果没有指定方法的深度，返回任何可用的深度
            if self.monster_depth is not None:
                return self.monster_depth, self.monster_depth_mask
            elif self.flow_depth is not None:
                return self.flow_depth, self.flow_depth_mask
            else:
                return None, None

    @property
    def effective_flow_depth(self):
        """为了兼容性，返回有效深度作为flow_depth"""
        depth, _ = self.get_effective_depth()
        return depth

    def _get_fused_depth(self):
        """融合MonSter和光流深度"""
        if self.monster_depth is None and not hasattr(self, 'flow_depth'):
            return None, None
        
        # 如果只有MonSter深度
        if self.monster_depth is not None and not hasattr(self, 'flow_depth'):
            return self.monster_depth, self.monster_depth_mask
        
        # 如果只有光流深度
        if self.monster_depth is None and hasattr(self, 'flow_depth'):
            return self.flow_depth, self.flow_depth_mask
        
        # 如果两种深度都有，进行融合
        if self.monster_depth is not None and hasattr(self, 'flow_depth'):
            monster_weight = 0.7  # MonSter权重更高
            flow_weight = 0.3
            
            # 只在两种深度都有效的区域进行融合
            valid_mask = self.monster_depth_mask & self.flow_depth_mask
            
            fused_depth = self.monster_depth.clone()
            fused_depth[valid_mask] = (
                monster_weight * self.monster_depth[valid_mask] + 
                flow_weight * self.flow_depth[valid_mask]
            )
            
            # 扩展掩码：包含任一方法有效的区域
            fused_mask = self.monster_depth_mask | self.flow_depth_mask
            
            return fused_depth, fused_mask
        
        return None, None


class MiniCam:
    def __init__(self, width, height, fovy, fovx, znear, zfar, world_view_transform, full_proj_transform):
        self.image_width = width
        self.image_height = height    
        self.FoVy = fovy
        self.FoVx = fovx
        self.znear = znear
        self.zfar = zfar
        self.world_view_transform = world_view_transform
        self.full_proj_transform = full_proj_transform
        view_inv = torch.inverse(self.world_view_transform)
        self.camera_center = view_inv[3][:3]

