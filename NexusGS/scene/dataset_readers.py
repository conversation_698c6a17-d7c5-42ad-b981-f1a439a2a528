#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#
import glob
from operator import index
import os
import sys

import matplotlib.pyplot as plt
from PIL import Image
import imageio
from typing import NamedTuple
from scene.colmap_loader import read_extrinsics_text, read_intrinsics_text, qvec2rotmat, rotmat2qvec, \
    read_extrinsics_binary, read_intrinsics_binary, read_points3D_binary, read_points3D_text
from utils.graphics_utils import getWorld2View2, focal2fov, fov2focal
from utils.general_utils import chamfer_dist
import numpy as np
import json
import cv2
import math
import torch
import open3d as o3d
from tqdm import tqdm
from pathlib import Path
from plyfile import PlyData, PlyElement
from utils.sh_utils import SH2RGB
from scene.gaussian_model import BasicPointCloud
from utils.flow_utils import readFlow

class CameraInfo(NamedTuple):
    uid: int
    R: np.array
    T: np.array
    K: np.array
    FovY: np.array
    FovX: np.array
    image: np.array
    flow: list
    image_path: str
    image_name: str
    width: int
    height: int
    mask: np.array
    bounds: np.array

class SceneInfo(NamedTuple):
    point_cloud: BasicPointCloud
    train_cameras: list
    test_cameras: list
    eval_cameras: list
    nerf_normalization: dict
    ply_path: str

def getNerfppNorm(cam_info):
    def get_center_and_diag(cam_centers):
        cam_centers = np.hstack(cam_centers)
        avg_cam_center = np.mean(cam_centers, axis=1, keepdims=True)
        center = avg_cam_center
        dist = np.linalg.norm(cam_centers - center, axis=0, keepdims=True)
        diagonal = np.max(dist)
        return center.flatten(), diagonal

    cam_centers = []

    for cam in cam_info:
        W2C = getWorld2View2(cam.R, cam.T)
        C2W = np.linalg.inv(W2C)
        cam_centers.append(C2W[:3, 3:4])

    center, diagonal = get_center_and_diag(cam_centers)
    radius = diagonal * 1.1

    translate = -center

    return {"translate": translate, "radius": radius}
'''这个函数没有用用的下面那个
def readColmapCameras2(cam_extrinsics, cam_intrinsics, images_folder):
    cam_infos = []
    for idx, key in enumerate(cam_extrinsics):
        sys.stdout.write('\r')
        # the exact output you're looking for:
        sys.stdout.write("Reading camera {}/{}".format(idx+1, len(cam_extrinsics)))
        sys.stdout.flush()

        extr = cam_extrinsics[key]
        intr = cam_intrinsics[extr.camera_id]
        height = intr.height
        width = intr.width

        uid = intr.id
        R = np.transpose(qvec2rotmat(extr.qvec))
        T = np.array(extr.tvec)

        if intr.model=="SIMPLE_PINHOLE":
            focal_length_x = intr.params[0]
            FovY = focal2fov(focal_length_x, height)
            FovX = focal2fov(focal_length_x, width)
        elif intr.model=="PINHOLE":
            focal_length_x = intr.params[0]
            focal_length_y = intr.params[1]
            FovY = focal2fov(focal_length_y, height)
            FovX = focal2fov(focal_length_x, width)
        else:
            assert False, "Colmap camera model not handled: only undistorted datasets (PINHOLE or SIMPLE_PINHOLE cameras) supported!"

        image_path = os.path.join(images_folder, os.path.basename(extr.name))
        image_name = os.path.basename(image_path).split(".")[0]
        image = Image.open(image_path)

        cam_info = CameraInfo(uid=uid, R=R, T=T, FovY=FovY, FovX=FovX, image=image,
                              image_path=image_path, image_name=image_name, width=width, height=height)
        cam_infos.append(cam_info)

        break

    def normalize(x):
        return x / np.linalg.norm(x)

    def viewmatrix(z, up, pos):
        vec2 = normalize(z)
        vec1_avg = up
        vec0 = normalize(np.cross(vec1_avg, vec2))
        vec1 = normalize(np.cross(vec2, vec0))
        m = np.stack([vec0, vec1, vec2, pos], 1)
        return m


    c2w = np.concatenate([R, T], dim=1)
    print(c2w.shape)
    ## Get spiral
    # Get average pose
    up = normalize(poses[:, :3, 1].sum(0))

    # Find a reasonable "focus depth" for this dataset
    close_depth, inf_depth = bds.min() * .9, bds.max() * 5.
    dt = .75
    mean_dz = 1. / (((1. - dt) / close_depth + dt / inf_depth))
    focal = mean_dz

    # Get radii for spiral path
    shrink_factor = .8
    zdelta = close_depth * .2
    tt = poses[:, :3, 3]  # ptstocam(poses[:3,3,:].T, c2w).T
    rads = np.percentile(np.abs(tt), 90, 0)
    c2w_path = c2w
    Num_views = 120
    rots = 2

    render_poses = []
    rads = np.array(list(rads) + [1.])
    hwf = c2w[:, 4:5]
    for theta in np.linspace(0., 2. * np.pi * rots, Num_views + 1)[:-1]:
        c = np.dot(c2w[:3, :4], np.array([np.cos(theta), -np.sin(theta), -np.sin(theta * 0.5), 1.]) * rads)
        z = normalize(c - np.dot(c2w[:3, :4], np.array([0, 0, -focal, 1.])))
        render_poses.append(np.concatenate([viewmatrix(z, up, c), hwf], 1))


    sys.stdout.write('\n')
    return cam_infos
'''
def readColmapCameras(cam_extrinsics, cam_intrinsics, images_folder, path, n_views, rgb_mapping, dataset_type='llff'):
    cam_infos = []
    model_zoe = None
    for idx, key in enumerate(sorted(cam_extrinsics.keys())):
        sys.stdout.write('\r')
        # the exact output you're looking for:
        sys.stdout.write("Reading camera {}/{}".format(idx+1, len(cam_extrinsics)))
        sys.stdout.flush()

        extr = cam_extrinsics[key]
        intr = cam_intrinsics[extr.camera_id]
        height = intr.height
        width = intr.width

        uid = intr.id
        R = np.transpose(qvec2rotmat(extr.qvec))
        T = np.array(extr.tvec)
        K = np.zeros((3,3)).astype(np.float32)
        K[2,2] = 1.
        try:
            bounds = np.load(os.path.join(path, 'poses_bounds.npy'))[idx, -2:]
        except:
            bounds = None
            
        if intr.model=="SIMPLE_PINHOLE" or intr.model=="SIMPLE_RADIAL":
            #print(intr.model, 'intr.model')
            #6/11SIMPLE_RADIAL
            focal_length_x = intr.params[0]
            K[0,0] = intr.params[0]
            K[1,1] = intr.params[0]
            K[0,2] = intr.params[1]
            K[1,2] = intr.params[2]
            FovY = focal2fov(focal_length_x, height)
            FovX = focal2fov(focal_length_x, width)
        elif intr.model=="PINHOLE":
            focal_length_x = intr.params[0]
            focal_length_y = intr.params[1]
            K[0,0] = intr.params[0]
            K[1,1] = intr.params[1]
            K[0,2] = intr.params[2]
            K[1,2] = intr.params[3]
            FovY = focal2fov(focal_length_y, height)
            FovX = focal2fov(focal_length_x, width)
        else:
            assert False, "Colmap camera model not handled: only undistorted datasets (PINHOLE or SIMPLE_PINHOLE cameras) supported!"

        image_path = os.path.join(images_folder, os.path.basename(extr.name))
        image_name = os.path.basename(image_path).split(".")[0]
        
        rgb_path = rgb_mapping[idx]   # os.path.join(images_folder, rgb_mapping[idx])
        rgb_name = os.path.basename(rgb_path).split(".")[0]
        image = Image.open(rgb_path)

        flow = []
        for i in range(n_views):
            for j in range(n_views):
                if i == j:
                    continue
                flow_path = os.path.join(path, str(n_views) + '_views/flow', rgb_name + f'_{i}_{j}.flo')
                if os.path.exists(flow_path):
                    flow.append((j, readFlow(flow_path)))

        cam_info = CameraInfo(uid=uid, R=R, T=T, K=K, FovY=FovY, FovX=FovX, image=image, flow=flow, image_path=image_path,
                image_name=image_name, width=width, height=height, mask=None, bounds=bounds)
        cam_infos.append(cam_info)

    sys.stdout.write('\n')
    return cam_infos


def farthest_point_sampling(points, k):
    """
    Sample k points from input pointcloud data points using Farthest Point Sampling.

    Parameters:
    points: numpy.ndarray
        The input pointcloud data, a numpy array of shape (N, D) where N is the
        number of points and D is the dimensionality of each point.
    k: int
        The number of points to sample.

    Returns:
    sampled_points: numpy.ndarray
        The sampled pointcloud data, a numpy array of shape (k, D).
    """
    N, D = points.shape
    farthest_pts = np.zeros((k, D))
    distances = np.full(N, np.inf)
    farthest = np.random.randint(0, N)
    for i in range(k):
        farthest_pts[i] = points[farthest]
        centroid = points[farthest]
        dist = np.sum((points - centroid) ** 2, axis=1)
        distances = np.minimum(distances, dist)
        farthest = np.argmax(distances)
    return farthest_pts


def fetchPly(path):
    plydata = PlyData.read(path)
    vertices = plydata['vertex']
    positions = np.vstack([vertices['x'], vertices['y'], vertices['z']]).T
    colors = np.vstack([vertices['red'], vertices['green'], vertices['blue']]).T / 255.0
    
    # 原始代码（会因为缺少法向量字段而报错）:
    # normals = np.vstack([vertices['nx'], vertices['ny'], vertices['nz']]).T
    
    # 修改后的代码：检查是否有法向量字段
    try:
        normals = np.vstack([vertices['nx'], vertices['ny'], vertices['nz']]).T
    except (ValueError, KeyError):
        # 如果没有法向量字段，创建零向量
        normals = np.zeros_like(positions)
        
    return BasicPointCloud(points=positions, colors=colors, normals=normals)


def storePly(path, xyz, rgb):
    # Define the dtype for the structured array
    dtype = [('x', 'f4'), ('y', 'f4'), ('z', 'f4'),
            ('nx', 'f4'), ('ny', 'f4'), ('nz', 'f4'),
            ('red', 'u1'), ('green', 'u1'), ('blue', 'u1')]

    normals = np.zeros_like(xyz)

    elements = np.empty(xyz.shape[0], dtype=dtype)
    attributes = np.concatenate((xyz, normals, rgb), axis=1)
    elements[:] = list(map(tuple, attributes))

    # Create the PlyData object and write to file
    vertex_element = PlyElement.describe(elements, 'vertex')
    ply_data = PlyData([vertex_element])
    ply_data.write(path)


def readColmapSceneInfo(path, images, eval, n_views=0, llffhold=8, dataset_type='llff'):
    #path=/root/autodl-tmp/1/new/NexusGS/datasets/LLFF/scene
    ply_path = os.path.join(path, "fused.ply")

    try:
        cameras_intrinsic_file = os.path.join(path, "sparse/0", "cameras.bin")
        cameras_extrinsic_file = os.path.join(path, "sparse/0", "images.bin")
        cam_extrinsics = read_extrinsics_binary(cameras_extrinsic_file)
        cam_intrinsics = read_intrinsics_binary(cameras_intrinsic_file)
    except:
        cameras_extrinsic_file = os.path.join(path, "sparse/0", "images.txt")
        cameras_intrinsic_file = os.path.join(path, "sparse/0", "cameras.txt")
        cam_extrinsics = read_extrinsics_text(cameras_extrinsic_file)
        cam_intrinsics = read_intrinsics_text(cameras_intrinsic_file)


    try:
        # 先测试能否读取PLY文件结构
        plydata = PlyData.read(ply_path)
        #print(f"PLY loaded successfully")
        #print(f"Elements: {[elem.name for elem in plydata.elements]}")
        
        # 再测试fetchPly函数
        pcd = fetchPly(ply_path)
        print(f"Successfully loaded PLY with {len(pcd.points)} points")
    except Exception as e:
        print(f"Failed to load PLY file: {e}")
        pcd = None
    
    
    reading_dir = "images" if images == None else images
    rgb_mapping = [f for f in sorted(glob.glob(os.path.join(path, reading_dir, '*')))
                   if f.endswith('JPG') or f.endswith('jpg') or f.endswith('png')]
    cam_extrinsics = {cam_extrinsics[k].name: cam_extrinsics[k] for k in cam_extrinsics}
    cam_infos_unsorted = readColmapCameras(cam_extrinsics=cam_extrinsics, cam_intrinsics=cam_intrinsics,
                             images_folder=os.path.join(path, reading_dir),  path=path, n_views=n_views, rgb_mapping=rgb_mapping, dataset_type=dataset_type)
    cam_infos = sorted(cam_infos_unsorted.copy(), key = lambda x : x.image_name)


    if dataset_type == 'dtu':
        if eval:
            train_idx = [25, 22, 28, 40, 44, 48, 0, 8, 13]
            exclude_idx = [1, 2, 9, 10, 11, 12, 14, 15, 23, 24, 26, 27, 29, 30, 31, 32, 33, 34, 35, 41, 42, 43, 45, 46, 47]
            train_cam_infos = [c for idx, c in enumerate(cam_infos) if idx in train_idx[:n_views]]
            test_cam_infos = [c for idx, c in enumerate(cam_infos) if idx in exclude_idx]
            train_cam_infos = sorted(train_cam_infos, key = lambda x : x.image_name)
            test_cam_infos = sorted(test_cam_infos, key = lambda x : x.image_name)

            eval_cam_infos = test_cam_infos
        else:
            train_cam_infos = cam_infos
            test_cam_infos = []
        
    elif dataset_type == 'blender':
        train_idxs = [2, 16, 26, 55, 73, 76, 86, 93]
        if eval:
            train_cam_infos = [c for idx, c in enumerate(cam_infos) if 'train' in c.image_name and int(c.image_name.split('_')[-1]) in train_idxs]
            test_cam_infos = [c for idx, c in enumerate(cam_infos) if 'train' not in c.image_name]
            eval_cam_infos = [c for idx, c in enumerate(test_cam_infos) if idx % llffhold != 0]
            test_cam_infos = [c for idx, c in enumerate(test_cam_infos) if idx % llffhold == 0]

    else:
        if eval:
            train_cam_infos = [c for idx, c in enumerate(cam_infos) if idx % llffhold != 0]
            test_cam_infos = [c for idx, c in enumerate(cam_infos) if idx % llffhold == 0]
            print('eval,有验证集')

        else:
            train_cam_infos = cam_infos
            test_cam_infos = []

        if n_views > 0:
            idx = list(range(len(train_cam_infos)))
            idx_sub = np.linspace(0, len(train_cam_infos)-1, n_views)
            idx_sub = [round(i) for i in idx_sub]
            train_cam_infos = [c for idx, c in enumerate(train_cam_infos) if idx in idx_sub]
            idx_eval = [i for i in idx if i not in idx_sub]
            eval_cam_infos = [c for idx, c in enumerate(cam_infos) if idx in idx_eval]
            assert len(train_cam_infos) == n_views
            
            # 在确定训练集后，生成对应的光流数据
           # flow_dir = generate_training_flow(path, train_cam_infos, n_views)
            #print(f"光流数据已生成并存储在: {flow_dir}")
    nerf_normalization = getNerfppNorm(train_cam_infos)
    scene_info = SceneInfo(point_cloud=pcd,
                           train_cameras=train_cam_infos,
                           test_cameras=test_cam_infos,
                           eval_cameras=eval_cam_infos,
                           nerf_normalization=nerf_normalization,
                           ply_path=ply_path)
    return scene_info


def readCamerasFromTransforms(path, transformsfile, white_background, extension=".png", n_views=8):
    cam_infos = []

    with open(os.path.join(path, transformsfile)) as json_file:
        contents = json.load(json_file)
        fovx = contents["camera_angle_x"]

        # skip = 8 if transformsfile == 'transforms_test.json' else 1
        # frames = contents["frames"][::skip]
        frames = contents["frames"]
        for idx, frame in tqdm(enumerate(frames)):
            cam_name = os.path.join(path, frame["file_path"] + extension)
            cam_name = frame["file_path"][2:] + extension

            # NeRF 'transform_matrix' is a camera-to-world transform
            c2w = np.array(frame["transform_matrix"])
            # change from OpenGL/Blender camera axes (Y up, Z back) to COLMAP (Y down, Z forward)
            c2w[:3, 1:3] *= -1

            # get the world-to-camera transform and set R, T
            w2c = np.linalg.inv(c2w)
            R = np.transpose(w2c[:3,:3])  # R is stored transposed due to 'glm' in CUDA code
            T = w2c[:3, 3]

            image_path = os.path.join(path, cam_name)
            image_name = Path(cam_name).stem
            image = Image.open(image_path)

            im_data = np.array(image.convert("RGBA"))

            bg = np.array([1,1,1]) if white_background else np.array([0, 0, 0])

            norm_data = im_data / 255.0
            arr = norm_data[:,:,:3] * norm_data[:, :, 3:4] + bg * (1 - norm_data[:, :, 3:4])
            image = Image.fromarray(np.array(arr*255.0, dtype=np.byte), "RGB")

            fovy = focal2fov(fov2focal(fovx, image.size[0]), image.size[1])
            FovY = fovy
            FovX = fovx

            K = np.zeros((3,3)).astype(np.float32)
            K[2,2] = 1.
            K[0,2] = image.size[1] / 2.
            K[1,2] = image.size[0] / 2.
            K[0,0] = K[1,1] = fov2focal(fovx, image.size[0])

            mask = norm_data[:, :, 3:4]
            # if skip == 1:
            #     depth_image = np.load('../SparseNeRF/depth_midas_temp_DPT_Hybrid/Blender/' +
            #                           image_path.split('/')[-4]+'/'+image_name+'_depth.npy')
            # else:
            #     depth_image = None

            arr = cv2.resize(arr, (400, 400))
            image = Image.fromarray(np.array(arr * 255.0, dtype=np.byte), "RGB")
            depth_image = None
            depth_image = None if depth_image is None else cv2.resize(depth_image, (400, 400))
            mask = None if mask is None else cv2.resize(mask, (400, 400))

            # checkpoints = 'things'
            flow = []
            for i in range(n_views):
                for j in range(n_views):
                    if i == j:
                        continue
                    flow_path = os.path.join(path, str(n_views) + '_views/flow', image_name + f'_{i}_{j}.flo')
                    if os.path.exists(flow_path):
                        flow.append((j, readFlow(flow_path)))


            cam_infos.append(CameraInfo(uid=idx, R=R, T=T, K=K, FovY=FovY, FovX=FovX, image=image, flow=flow, image_path=image_path,
                                        image_name=image_name, width=image.size[0], height=image.size[1],
                                        mask=mask, bounds=None))
    return cam_infos


'''
def readNerfSyntheticInfo(path, white_background, eval, n_views=0, llffhold=8, extension=".png"):
    print("Reading Training Transforms")
    train_cam_infos = readCamerasFromTransforms(path, "transforms_train.json", white_background, extension, n_views)
    print("Reading Test Transforms")
    test_cam_infos = readCamerasFromTransforms(path, "transforms_test.json", white_background, extension, n_views)

    if not eval:
        train_cam_infos.extend(test_cam_infos)
        test_cam_infos = []

    pseudo_cam_infos = train_cam_infos #train_cam_infos
    if n_views > 0:
        if n_views > 0:
            train_cam_infos = [c for idx, c in enumerate(train_cam_infos) if idx in [2, 16, 26, 55, 73, 76, 86, 93]]
        eval_cam_infos = test_cam_infos
        test_cam_infos = [c for idx, c in enumerate(test_cam_infos) if idx % llffhold == 0]

    nerf_normalization = getNerfppNorm(train_cam_infos)

    ply_path = os.path.join(path, str(n_views) + "_views/dense/fused.ply")

    print('train', [info.image_path for info in train_cam_infos])
    print('eval', [info.image_path for info in eval_cam_infos])

    try:
        pcd = fetchPly(ply_path)
    except:
        pcd = None


    scene_info = SceneInfo(point_cloud=pcd,
                           train_cameras=train_cam_infos,
                           test_cameras=test_cam_infos,
                           eval_cameras=eval_cam_infos,
                           nerf_normalization=nerf_normalization,
                           ply_path=ply_path)
    return scene_info

'''
sceneLoadTypeCallbacks = {
    "Colmap": readColmapSceneInfo
   # "Blender" : readNerfSyntheticInfo
}

def generate_training_flow(scene_path, train_cam_infos, n_views):
    """
    为训练集图片生成光流数据
    
    Args:
        scene_path: 场景路径
        train_cam_infos: 训练集相机信息列表
        n_views: 训练视图数量
        
    Returns:
        flow_dir: 光流文件存储目录路径
    """
    import subprocess
    import shutil
    
    # 创建训练数据目录
    views_dir = os.path.join(scene_path, f"{n_views}_views")
    flow_dir = os.path.join(views_dir, "flow")
    
    os.makedirs(views_dir, exist_ok=True)
    os.makedirs(flow_dir, exist_ok=True)
    
    print(f"准备训练集光流数据，共{n_views}个视图...")
    
    # 复制训练集图片到views_dir
    for i, cam_info in enumerate(train_cam_infos):
        src_path = cam_info.image_path
        dst_path = os.path.join(views_dir, os.path.basename(src_path))
        if not os.path.exists(dst_path):
            shutil.copy2(src_path, dst_path)
            print(f"复制训练图片[{i}]: {os.path.basename(src_path)}")
    

    # 生成光流
    flowformer_path = "/root/autodl-tmp/1/new/FlowFormerPlusPlus"
    if os.path.exists(flowformer_path):
        print("开始生成光流数据...")
        print(views_dir, 'views_dir')
        print(flow_dir, 'flow_dir')
        cmd = [
            "python", "-u", os.path.join(flowformer_path, "tools/gen_allpairs_flow.py"),
            "--img_dir", os.path.abspath(views_dir),
            "--out_dir", os.path.abspath(flow_dir),
            "--model", os.path.join(flowformer_path, "checkpoints/sintel.pth")
        ]
        
        original_cwd = os.getcwd()
        try:
            os.chdir(flowformer_path)
            env = os.environ.copy()
            env["PYTHONPATH"] = flowformer_path
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 光流生成成功！")
            else:
                print(f"⚠️ 光流生成失败，将使用空光流: {result.stderr}")
        except Exception as e:
            print(f"⚠️ 光流生成出错，将使用空光流: {e}")
        finally:
            os.chdir(original_cwd)
    else:
        print(f"⚠️ FlowFormer路径不存在: {flowformer_path}")
    
    return flow_dir
