#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#

import os
import random
import json
import numpy as np
# import torch
from utils.system_utils import searchForMaxIteration
from scene.dataset_readers import sceneLoadTypeCallbacks
from scene.gaussian_model import GaussianModel
from scene.hf_gaussian_model import HFGaussianModel
from arguments import ModelParams
from utils.camera_utils import cameraList_from_camInfos, camera_to_JSON, cameraList_from_huggingfaceModel
from utils.pose_utils import generate_random_poses_llff, generate_random_poses_360
# from gaussian_renderer import render
from utils.flow_utils import compute_depth_by_flow, construct_pcd, generate_optical_flow
from utils.graphics_utils import BasicPointCloud
import torch

class Scene:

    gaussians : GaussianModel

    def __init__(self, args : ModelParams, gaussians : GaussianModel, load_iteration=None, shuffle=True, resolution_scales=[1.0]):
        """b
        :param path: Path to colmap scene main folder.
        """
        self.model_path = args.model_path
        self.loaded_iter = None
        self.gaussians = gaussians
#如果load_iteration不为空，则加载checkpoint
        if load_iteration:
            if load_iteration == -1:
                self.loaded_iter = searchForMaxIteration(os.path.join(self.model_path, "point_cloud"))
            else:
                self.loaded_iter = load_iteration
            print("Loading trained model at iteration {}".format(self.loaded_iter))
# 初始化相机列表
        self.train_cameras = {}
        self.test_cameras = {}
        self.pseudo_cameras = {}
        self.eval_cameras = {}
        #如果source_path存在sparse文件夹，则使用Colmap加载数据，否则使用Blender加载数据
        if os.path.exists(os.path.join(args.source_path, "sparse")):
            scene_info = sceneLoadTypeCallbacks["Colmap"](args.source_path, args.images, args.eval, args.n_views, dataset_type=args.dataset_type)
        elif os.path.exists(os.path.join(args.source_path, "transforms_train.json")):
            print("Found transforms_train.json file, assuming Blender data set!")
            scene_info = sceneLoadTypeCallbacks["Blender"](args.source_path, args.white_background, args.eval, args.n_views)
        else:
            assert False, "Could not recognize scene type!"


        if not self.loaded_iter:
            # with open(scene_info.ply_path, 'rb') as src_file, open(os.path.join(self.model_path, "input.ply") , 'wb') as dest_file:
            #     dest_file.write(src_file.read())
            json_cams = []
            camlist = []
            if scene_info.test_cameras:
                camlist.extend(scene_info.test_cameras)
            if scene_info.train_cameras:
                camlist.extend(scene_info.train_cameras)
            for id, cam in enumerate(camlist):
                json_cams.append(camera_to_JSON(id, cam))
            with open(os.path.join(self.model_path, "cameras.json"), 'w') as file:
                json.dump(json_cams, file)

        if shuffle:
            random.shuffle(scene_info.train_cameras)  # Multi-res consistent random shuffling
            random.shuffle(scene_info.test_cameras)  # Multi-res consistent random shuffling

        self.cameras_extent = scene_info.nerf_normalization["radius"]
        print(self.cameras_extent, 'cameras_extent')

        for resolution_scale in resolution_scales:
            print("Loading Training Cameras")
            self.train_cameras[resolution_scale] = cameraList_from_camInfos(scene_info.train_cameras, resolution_scale, args, scene_info.point_cloud)
            print("Loading Test Cameras")
            self.test_cameras[resolution_scale] = cameraList_from_camInfos(scene_info.test_cameras, resolution_scale, args)
            print("Loading Eval Cameras")
            self.eval_cameras[resolution_scale] = cameraList_from_camInfos(scene_info.eval_cameras, resolution_scale, args)


        if self.loaded_iter:
            self.gaussians.load_ply(os.path.join(self.model_path, "point_cloud", 
                                                   f"iteration_{self.loaded_iter}", "point_cloud.ply"))
        else:
            # 根据深度方法选择处理流程
            if hasattr(args, 'depth_method') and args.depth_method == "monster":
                self._init_with_monster_depth(args, resolution_scales, scene_info)
            else:
                self._init_with_flow_depth(args, resolution_scales, scene_info)

    def _init_with_monster_depth(self, args, resolution_scales, scene_info):
        """使用MonSter深度初始化"""
        from utils.monster_utils import run_monster_preprocessing, compute_depth_by_monster, construct_pcd_from_monster
        
        # 1. 运行MonSter预处理
        run_monster_preprocessing(args.source_path)
        
        # 2. 为所有分辨率的相机加载深度
        for resolution_scale in resolution_scales:
            compute_depth_by_monster(self.train_cameras[resolution_scale])
        
        # 3. 从深度构建点云
        self.pcd = construct_pcd_from_monster(self.train_cameras[1.0])
        
        # 4. 初始化高斯
        self.gaussians.create_from_pcd(self.pcd, None, self.cameras_extent, args.drop_rate)

    def _init_with_flow_depth(self, args, resolution_scales, scene_info):
        """使用原有光流深度初始化"""
        for resolution_scale in resolution_scales:
            generate_optical_flow(scene_info.train_cameras, args.source_path, args.n_views)
            compute_depth_by_flow(self.train_cameras[resolution_scale], 
                                args.valid_dis_threshold, args.near_n)
        
        self.pcd = construct_pcd(self.train_cameras[1.0])
        self.gaussians.create_from_pcd(self.pcd, None, self.cameras_extent, args.drop_rate)

    def save(self, iteration):
        point_cloud_path = os.path.join(self.model_path, "point_cloud/iteration_{}".format(iteration))
        self.gaussians.save_ply(os.path.join(point_cloud_path, "point_cloud.ply"))
    
    def getTrainCameras(self, scale=1.0):
        return self.train_cameras[scale]

    def getTestCameras(self, scale=1.0):
        return self.test_cameras[scale]
    
    def getEvalCameras(self, scale=1.0):
        return self.eval_cameras[scale]


class HFScene:

    gaussians : GaussianModel

    def __init__(self, args : ModelParams, gaussians : GaussianModel, load_iteration=None, shuffle=True, resolution_scales=[1.0]):
        """b
        :param path: Path to colmap scene main folder.
        """
        self.model_path = args.model_path
        self.loaded_iter = load_iteration
        self.gaussians = gaussians

        self.train_cameras = {}
        self.test_cameras = {}
        self.pseudo_cameras = {}
        self.eval_cameras = {}

        model = HFGaussianModel.from_pretrained(args.source_path, revision=args.revision)

        self.cameras_extent = model.cameras_extent

        print("Loading Training Cameras")
        self.train_cameras[1.0] = cameraList_from_huggingfaceModel(args, model, "train")
        print("Loading Test Cameras")
        self.test_cameras[1.0] = cameraList_from_huggingfaceModel(args, model, "test")

        points = model.points.data.cpu().numpy()
        colors = model.colors.data.cpu().numpy()

        if self.loaded_iter:
            self.gaussians.load_ply(os.path.join(self.model_path,
                                                           "point_cloud",
                                                           "iteration_" + str(self.loaded_iter),
                                                           "point_cloud.ply"))
        else:
            self.pcd = BasicPointCloud(points=points, colors=colors, normals=np.zeros_like(points))
            self.gaussians.create_from_pcd(self.pcd, None, self.cameras_extent, args.drop_rate)

    
    def save(self, iteration):
        point_cloud_path = os.path.join(self.model_path, "point_cloud/iteration_{}".format(iteration))
        self.gaussians.save_ply(os.path.join(point_cloud_path, "point_cloud.ply"))
    
    def getTrainCameras(self, scale=1.0):
        return self.train_cameras[scale]

    def getTestCameras(self, scale=1.0):
        return self.test_cameras[scale]
    
    def getEvalCameras(self, scale=1.0):
        return self.eval_cameras[scale]
