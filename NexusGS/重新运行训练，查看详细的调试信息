[5000, 10000, 30000]
Optimizing output/llff/scene
Output folder: output/llff/scene [24/07 20:13:15]
Successfully loaded PLY with 1447 points [24/07 20:13:15]

Reading camera 1/10idex 0 [24/07 20:13:15]

Reading camera 2/10idex 1 [24/07 20:13:15]

Reading camera 3/10idex 2 [24/07 20:13:15]

Reading camera 4/10idex 3 [24/07 20:13:15]

Reading camera 5/10idex 4 [24/07 20:13:15]

Reading camera 6/10idex 5 [24/07 20:13:15]

Reading camera 7/10idex 6 [24/07 20:13:15]

Reading camera 8/10idex 7 [24/07 20:13:15]

Reading camera 9/10idex 8 [24/07 20:13:15]

Reading camera 10/10idex 9 [24/07 20:13:15]
 [24/07 20:13:15]
eval,有验证集 [24/07 20:13:15]
5.762255191802979 cameras_extent [24/07 20:13:15]
Loading Training Cameras [24/07 20:13:15]

0it [00:00, ?it/s]
1it [00:00,  5.91it/s]
3it [00:00,  9.46it/s]
4it [00:00,  9.60it/s]
Loading Test Cameras [24/07 20:13:16]

0it [00:00, ?it/s]
2it [00:00, 17.40it/s]
2it [00:00, 17.38it/s]
Loading Eval Cameras [24/07 20:13:16]

0it [00:00, ?it/s]
3it [00:00, 23.95it/s]
4it [00:00, 25.39it/s]
Running MonSter preprocessing for datasets/LLFF/scene [24/07 20:13:16]
MonSter depth already exists, skipping preprocessing [24/07 20:13:16]
Loading MonSter depth maps to cameras... [24/07 20:13:16]
Processing camera 0: image0002 [24/07 20:13:16]
Warning: No MonSter depth found for image0002 [24/07 20:13:16]
Processing camera 1: image0004 [24/07 20:13:16]
Warning: No MonSter depth found for image0004 [24/07 20:13:16]
Processing camera 2: image0007 [24/07 20:13:16]
Warning: No MonSter depth found for image0007 [24/07 20:13:16]
Processing camera 3: image0010 [24/07 20:13:16]
Warning: No MonSter depth found for image0010 [24/07 20:13:16]
MonSter depth loading: 0/4 successful [24/07 20:13:16]
Traceback (most recent call last):
  File "/root/autodl-tmp/1/new/NexusGS/train.py", line 339, in <module>
    training(lp.extract(args), op.extract(args), pp.extract(args), args)
  File "/root/autodl-tmp/1/new/NexusGS/train.py", line 75, in training
    scene = Scene(args, gaussians, shuffle=False)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/autodl-tmp/1/new/NexusGS/scene/__init__.py", line 98, in __init__
    self._init_with_monster_depth(args, resolution_scales, scene_info)
  File "/root/autodl-tmp/1/new/NexusGS/scene/__init__.py", line 111, in _init_with_monster_depth
    compute_depth_by_monster(self.train_cameras[resolution_scale])
  File "/root/autodl-tmp/1/new/NexusGS/utils/monster_utils.py", line 204, in compute_depth_by_monster
    raise RuntimeError("No MonSter depth data loaded successfully")
RuntimeError: No MonSter depth data loaded successfully
Looking for config file in output/llff/scene/cfg_args
Config file found: output/llff/scene/cfg_args
Rendering output/llff/scene
Loading trained model at iteration 10000 [24/07 20:13:21]
Successfully loaded PLY with 1447 points [24/07 20:13:21]

Reading camera 1/10idex 0 [24/07 20:13:21]

Reading camera 2/10idex 1 [24/07 20:13:21]

Reading camera 3/10idex 2 [24/07 20:13:21]

Reading camera 4/10idex 3 [24/07 20:13:21]

Reading camera 5/10idex 4 [24/07 20:13:21]

Reading camera 6/10idex 5 [24/07 20:13:21]

Reading camera 7/10idex 6 [24/07 20:13:21]

Reading camera 8/10idex 7 [24/07 20:13:21]

Reading camera 9/10idex 8 [24/07 20:13:21]

Reading camera 10/10idex 9 [24/07 20:13:21]
 [24/07 20:13:21]
eval,有验证集 [24/07 20:13:21]
5.762255191802979 cameras_extent [24/07 20:13:21]
Loading Training Cameras [24/07 20:13:21]

0it [00:00, ?it/s]
1it [00:00,  3.27it/s]
3it [00:00,  7.15it/s]
4it [00:00,  7.32it/s]
Loading Test Cameras [24/07 20:13:22]

0it [00:00, ?it/s]
2it [00:00, 19.43it/s]
2it [00:00, 19.40it/s]
Loading Eval Cameras [24/07 20:13:22]

0it [00:00, ?it/s]
3it [00:00, 25.59it/s]
4it [00:00, 25.17it/s]

Rendering progress:   0%|          | 0/4 [00:00<?, ?it/s]/root/autodl-tmp/1/new/NexusGS/render.py:120: MatplotlibDeprecationWarning: The get_cmap function was deprecated in Matplotlib 3.7 and will be removed in 3.11. Use ``matplotlib.colormaps[name]`` or ``matplotlib.colormaps.get_cmap()`` or ``pyplot.get_cmap()`` instead.
  depth_map = visualize_cmap(depth_est, np.ones_like(depth_est), cm.get_cmap('turbo'), curve_fn=depth_curve_fn).copy()

Rendering progress:  25%|██▌       | 1/4 [00:01<00:04,  1.51s/it]
Rendering progress:  50%|█████     | 2/4 [00:03<00:03,  1.52s/it]
Rendering progress:  75%|███████▌  | 3/4 [00:04<00:01,  1.47s/it]
Rendering progress: 100%|██████████| 4/4 [00:06<00:00,  1.55s/it]
Rendering progress: 100%|██████████| 4/4 [00:06<00:00,  1.53s/it]

Rendering progress:   0%|          | 0/2 [00:00<?, ?it/s]
Rendering progress:  50%|█████     | 1/2 [00:01<00:01,  1.77s/it]
Rendering progress: 100%|██████████| 2/2 [00:03<00:00,  1.77s/it]
Rendering progress: 100%|██████████| 2/2 [00:03<00:00,  1.77s/it]

Scene: output/llff/scene
Method: ours_10000

Metric evaluation progress:   0%|          | 0/2 [00:00<?, ?it/s]
Metric evaluation progress:  50%|█████     | 1/2 [00:01<00:01,  1.62s/it]
Metric evaluation progress: 100%|██████████| 2/2 [00:02<00:00,  1.46s/it]
Metric evaluation progress: 100%|██████████| 2/2 [00:02<00:00,  1.49s/it]
  SSIM :    0.6082382
  PSNR :   18.9058990
  LPIPS:    0.4895031

