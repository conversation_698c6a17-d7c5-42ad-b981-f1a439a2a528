#!/usr/bin/env python3

import sys
sys.path.append('.')

def fix_test_camera_fov():
    """修复测试相机的视场角"""
    
    # 重新训练时强制使用统一的分辨率和内参
    import subprocess
    
    cmd = [
        "python", "train.py",
        "-s", "datasets/LLFF/scene",
        "-m", "output/llff/scene_fixed",
        "--resolution", "1",  # 使用原始分辨率
        "--depth_method", "monster",
        "--test_iterations", "1000", "5000", "10000",
        "--save_iterations", "1000", "5000", "10000"
    ]
    
    print("重新训练以修复相机参数...")
    print(" ".join(cmd))
    subprocess.run(cmd)

if __name__ == "__main__":
    fix_test_camera_fov()