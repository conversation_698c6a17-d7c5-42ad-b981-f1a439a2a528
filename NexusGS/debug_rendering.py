#!/usr/bin/env python3

import sys
sys.path.append('.')

import torch
from scene import Scene
from scene.gaussian_model import GaussianModel
from gaussian_renderer import render
from arguments import ModelParams, PipelineParams
from argparse import ArgumentParser
import numpy as np
from PIL import Image

def debug_rendering():
    """调试渲染过程"""
    
    parser = ArgumentParser()
    args = ModelParams(parser).parse_args([
        "-s", "datasets/LLFF/scene",
        "-m", "output/llff/scene",
        "--depth_method", "monster"
    ])
    
    pipe_args = PipelineParams(parser).parse_args([])
    
    # 加载训练好的模型
    gaussians = GaussianModel(args.sh_degree)
    scene = Scene(args, gaussians, load_iteration=10000)
    
    train_cams = scene.getTrainCameras()
    test_cams = scene.getTestCameras()
    
    print("=== 渲染调试 ===")
    
    # 渲染一个训练相机
    if train_cams:
        train_cam = train_cams[0]
        print(f"渲染训练相机 0...")
        
        with torch.no_grad():
            train_render = render(train_cam, gaussians, pipe_args)
            train_image = train_render["render"]
            
        print(f"训练渲染结果: {train_image.shape}")
        print(f"训练相机尺寸: {train_cam.image_width}x{train_cam.image_height}")
        
        # 保存训练渲染结果
        train_img_np = (train_image.permute(1,2,0).cpu().numpy() * 255).astype(np.uint8)
        Image.fromarray(train_img_np).save("debug_train_render.png")
        print("保存训练渲染: debug_train_render.png")
    
    # 渲染一个测试相机
    if test_cams:
        test_cam = test_cams[0]
        print(f"\n渲染测试相机 0...")
        
        with torch.no_grad():
            test_render = render(test_cam, gaussians, pipe_args)
            test_image = test_render["render"]
            
        print(f"测试渲染结果: {test_image.shape}")
        print(f"测试相机尺寸: {test_cam.image_width}x{test_cam.image_height}")
        
        # 保存测试渲染结果
        test_img_np = (test_image.permute(1,2,0).cpu().numpy() * 255).astype(np.uint8)
        Image.fromarray(test_img_np).save("debug_test_render.png")
        print("保存测试渲染: debug_test_render.png")
        
        # 对比渲染结果尺寸
        if train_cams:
            print(f"\n=== 渲染结果对比 ===")
            print(f"训练渲染尺寸: {train_image.shape}")
            print(f"测试渲染尺寸: {test_image.shape}")
            
            if train_image.shape != test_image.shape:
                print("⚠️  警告: 训练和测试渲染尺寸不一致!")

if __name__ == "__main__":
    debug_rendering()