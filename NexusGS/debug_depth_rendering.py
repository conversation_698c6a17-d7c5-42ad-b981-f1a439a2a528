#!/usr/bin/env python3

import sys
sys.path.append('.')

import torch
from scene import Scene
from scene.gaussian_model import GaussianModel
from arguments import ModelParams, PipelineParams, OptimizationParams
from argparse import ArgumentParser
from gaussian_renderer import render
import numpy as np
from PIL import Image
import os

def debug_depth_rendering():
    """调试深度图和渲染尺寸问题"""
    
    parser = ArgumentParser(description="Debug depth and rendering")
    lp = ModelParams(parser)
    pp = PipelineParams(parser)
    op = OptimizationParams(parser)
    
    # 使用存在的images文件夹
    args = parser.parse_args([
        "-s", "datasets/LLFF/scene",
        "--images", "images",  # 使用原始images文件夹
        "--depth_method", "monster"
    ])
    
    model_params = lp.extract(args)
    pipe_params = pp.extract(args)
    
    print(f"使用图像文件夹: {model_params.images}")
    
    try:
        gaussians = GaussianModel(model_params)
        scene = Scene(model_params, gaussians)
        
        train_cams = scene.getTrainCameras()
        test_cams = scene.getTestCameras()
        
        print("=== 深度图和渲染尺寸调试 ===")
        print(f"训练相机数: {len(train_cams)}")
        print(f"测试相机数: {len(test_cams)}")
        
        # 检查训练相机的深度图
        if train_cams:
            train_cam = train_cams[0]
            print(f"\n=== 训练相机 0 ===")
            print(f"相机配置尺寸: {train_cam.image_width}×{train_cam.image_height}")
            print(f"原始图像shape: {train_cam.original_image.shape}")
            
            if hasattr(train_cam, 'monster_depth') and train_cam.monster_depth is not None:
                print(f"MonSter深度shape: {train_cam.monster_depth.shape}")
                print(f"深度值范围: {train_cam.monster_depth.min():.4f} ~ {train_cam.monster_depth.max():.4f}")
            
            # 渲染训练相机
            with torch.no_grad():
                train_render = render(train_cam, gaussians, pipe_params)
                train_image = train_render["render"]
                
            print(f"训练渲染结果shape: {train_image.shape}")
            
            # 保存训练渲染结果
            train_img_np = (train_image.permute(1,2,0).cpu().numpy() * 255).astype(np.uint8)
            Image.fromarray(train_img_np).save("debug_train_render.png")
            print("保存训练渲染: debug_train_render.png")
        
        # 检查测试相机的深度图
        if test_cams:
            test_cam = test_cams[0]
            print(f"\n=== 测试相机 0 ===")
            print(f"相机配置尺寸: {test_cam.image_width}×{test_cam.image_height}")
            print(f"原始图像shape: {test_cam.original_image.shape}")
            
            if hasattr(test_cam, 'monster_depth') and test_cam.monster_depth is not None:
                print(f"MonSter深度shape: {test_cam.monster_depth.shape}")
                print(f"深度值范围: {test_cam.monster_depth.min():.4f} ~ {test_cam.monster_depth.max():.4f}")
            else:
                print("⚠️  测试相机没有MonSter深度!")
            
            # 渲染测试相机
            with torch.no_grad():
                test_render = render(test_cam, gaussians, pipe_params)
                test_image = test_render["render"]
                
            print(f"测试渲染结果shape: {test_image.shape}")
            
            # 保存测试渲染结果
            test_img_np = (test_image.permute(1,2,0).cpu().numpy() * 255).astype(np.uint8)
            Image.fromarray(test_img_np).save("debug_test_render.png")
            print("保存测试渲染: debug_test_render.png")
        
        # 对比分析
        if train_cams and test_cams:
            train_cam = train_cams[0]
            test_cam = test_cams[0]
            
            print(f"\n=== 对比分析 ===")
            
            # 相机配置对比
            print(f"相机配置尺寸:")
            print(f"  训练: {train_cam.image_width}×{train_cam.image_height}")
            print(f"  测试: {test_cam.image_width}×{test_cam.image_height}")
            
            # 图像tensor对比
            print(f"图像tensor尺寸:")
            print(f"  训练: {train_cam.original_image.shape}")
            print(f"  测试: {test_cam.original_image.shape}")
            
            # 深度图对比
            train_has_depth = hasattr(train_cam, 'monster_depth') and train_cam.monster_depth is not None
            test_has_depth = hasattr(test_cam, 'monster_depth') and test_cam.monster_depth is not None
            
            print(f"深度图状态:")
            print(f"  训练有深度: {train_has_depth}")
            print(f"  测试有深度: {test_has_depth}")
            
            if train_has_depth and test_has_depth:
                print(f"深度图尺寸:")
                print(f"  训练: {train_cam.monster_depth.shape}")
                print(f"  测试: {test_cam.monster_depth.shape}")
                
                # 检查深度图尺寸是否匹配相机配置
                train_depth_match = (train_cam.monster_depth.shape[0] == train_cam.image_height and 
                                   train_cam.monster_depth.shape[1] == train_cam.image_width)
                test_depth_match = (test_cam.monster_depth.shape[0] == test_cam.image_height and 
                                  test_cam.monster_depth.shape[1] == test_cam.image_width)
                
                print(f"深度图与相机尺寸匹配:")
                print(f"  训练匹配: {train_depth_match}")
                print(f"  测试匹配: {test_depth_match}")
                
                if not test_depth_match:
                    print("❌ 测试相机的深度图尺寸与相机配置不匹配!")
                    print(f"   深度图: {test_cam.monster_depth.shape}")
                    print(f"   相机配置: {test_cam.image_height}×{test_cam.image_width}")
        
            # 渲染结果对比
            if 'train_image' in locals() and 'test_image' in locals():
                print(f"渲染结果尺寸:")
                print(f"  训练: {train_image.shape}")
                print(f"  测试: {test_image.shape}")
                
                if train_image.shape != test_image.shape:
                    print("❌ 训练和测试渲染结果尺寸不一致!")
                    height_ratio = test_image.shape[1] / train_image.shape[1]
                    width_ratio = test_image.shape[2] / train_image.shape[2]
                    print(f"   尺寸比例: H={height_ratio:.4f}, W={width_ratio:.4f}")

    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_depth_rendering()
