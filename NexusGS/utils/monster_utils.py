"""
MonSter深度集成工具
用于将MonSter生成的深度信息集成到NexusGS训练流程中
"""

import torch
import numpy as np
import os
import cv2
import shutil
import subprocess
from PIL import Image
from utils.graphics_utils import BasicPointCloud

def run_monster_preprocessing(dataset_path):
    """运行MonSter深度预处理"""
    print(f"Running MonSter preprocessing for {dataset_path}")
    
    # 检查是否已经有深度输出
    depth_output_dir = os.path.join(dataset_path, "monster_stereo/depth_output")
    if os.path.exists(depth_output_dir) and len(os.listdir(depth_output_dir)) > 0:
        print("MonSter depth already exists, skipping preprocessing")
        return
    
    # 实际运行 MonSter 预处理
    try:
        # 1. 准备立体对 - 检查images子目录
        images_dir = os.path.join(dataset_path, "images")
        if not os.path.exists(images_dir):
            # 如果没有images子目录，直接使用dataset_path
            images_dir = dataset_path
        
        stereo_dir = os.path.join(dataset_path, "monster_stereo")
        
        # 检查是否有图像文件
        image_files = [f for f in os.listdir(images_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        if len(image_files) == 0:
            raise ValueError(f"No image files found in: {images_dir}")
        
        print(f"Found {len(image_files)} images in {images_dir}")
        print("Preparing stereo pairs...")
        prepare_stereo_pairs(images_dir, stereo_dir)
        
        # 2. 运行 MonSter 推理
        print("Running MonSter inference...")
        run_monster_inference(stereo_dir)
        
        print("MonSter preprocessing completed successfully!")
        
    except Exception as e:
        print(f"MonSter preprocessing failed: {e}")
        # 创建空目录避免重复尝试
        os.makedirs(depth_output_dir, exist_ok=True)
        raise

def prepare_stereo_pairs(images_dir, output_dir):
    """准备MonSter需要的立体对"""
    # 创建输出目录
    left_dir = os.path.join(output_dir, "left")
    right_dir = os.path.join(output_dir, "right")
    os.makedirs(left_dir, exist_ok=True)
    os.makedirs(right_dir, exist_ok=True)
    
    # 获取所有图像文件
    image_files = sorted([f for f in os.listdir(images_dir) 
                         if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    
    if len(image_files) < 2:
        raise ValueError("Need at least 2 images for stereo pairs")
    
    print(f"Found {len(image_files)} images, creating stereo pairs...")
    
    # 检查第一张图像的尺寸作为参考
    first_img_path = os.path.join(images_dir, image_files[0])
    first_img = cv2.imread(first_img_path)
    reference_height, reference_width = first_img.shape[:2]
    print(f"参考图像尺寸: {reference_width}x{reference_height}")
    
    # 为每张图像创建立体对
    for i, img_file in enumerate(image_files):
        base_name = os.path.splitext(img_file)[0]
        
        # 选择立体对策略
        if i == 0:
            left_src = os.path.join(images_dir, image_files[i])
            right_src = os.path.join(images_dir, image_files[i + 1])
        elif i == len(image_files) - 1:
            left_src = os.path.join(images_dir, image_files[i - 1])
            right_src = os.path.join(images_dir, image_files[i])
        else:
            left_src = os.path.join(images_dir, image_files[i])
            right_src = os.path.join(images_dir, image_files[i + 1])
        
        # 目标文件路径
        left_dst = os.path.join(left_dir, f"{base_name}_L.png")
        right_dst = os.path.join(right_dir, f"{base_name}_R.png")
        
        # 读取、调整尺寸并保存
        left_img = cv2.imread(left_src)
        right_img = cv2.imread(right_src)
        
        # 确保所有图像尺寸一致
        if left_img.shape[:2] != (reference_height, reference_width):
            left_img = cv2.resize(left_img, (reference_width, reference_height))
        if right_img.shape[:2] != (reference_height, reference_width):
            right_img = cv2.resize(right_img, (reference_width, reference_height))
        
        cv2.imwrite(left_dst, left_img)
        cv2.imwrite(right_dst, right_img)
        
        print(f"  Created stereo pair for {img_file} (尺寸: {reference_width}x{reference_height})")

def run_monster_inference(stereo_dir):
    """运行MonSter推理"""
    monster_path = "/root/autodl-tmp/1/new/MonSter"
    
    if not os.path.exists(monster_path):
        raise ValueError(f"MonSter path not found: {monster_path}")
    
    # 检查模型文件
    model_path = os.path.join(monster_path, "pretrained/mix_all.pth")
    if not os.path.exists(model_path):
        raise ValueError(f"MonSter model not found: {model_path}")
    
    # 构建命令 - 使用绝对路径
    left_pattern = os.path.abspath(os.path.join(stereo_dir, "left/*.png"))
    right_pattern = os.path.abspath(os.path.join(stereo_dir, "right/*.png"))
    output_dir = os.path.abspath(os.path.join(stereo_dir, "depth_output"))
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    cmd = [
        "python", "demo_video.py",
        "--restore_ckpt", model_path,
        "-l", left_pattern,
        "-r", right_pattern,
        "--output_directory", output_dir,
        "--encoder", "vitl"
    ]
    
    print(f"Running MonSter inference...")
    print(f"Command: {' '.join(cmd)}")
    print(f"Left pattern: {left_pattern}")
    print(f"Right pattern: {right_pattern}")
    print(f"Output dir: {output_dir}")
    
    # 设置环境变量禁用 xFormers
    env = os.environ.copy()
    env['XFORMERS_DISABLED'] = '1'
    
    # 切换到MonSter目录并运行
    original_cwd = os.getcwd()
    try:
        os.chdir(monster_path)
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        
        if result.returncode != 0:
            print(f"MonSter stdout: {result.stdout}")
            print(f"MonSter stderr: {result.stderr}")
            raise RuntimeError(f"MonSter inference failed: {result.stderr}")
        
        print("MonSter inference completed successfully!")
        
    finally:
        os.chdir(original_cwd)

def load_monster_depth(depth_file, target_height=None, target_width=None):
    """加载MonSter深度数据并调整尺寸"""
    print(f"Loading depth file: {depth_file}")
    
    # 加载深度数据
    depth = np.load(depth_file)
    print(f"原始深度尺寸: {depth.shape}")
    
    # 如果提供了目标尺寸，进行调整
    if target_height is not None and target_width is not None:
        current_h, current_w = depth.shape[:2]
        print(f"目标尺寸: {target_width}x{target_height}")
        
        if current_h != target_height or current_w != target_width:
            print(f"调整深度图尺寸: {current_w}x{current_h} -> {target_width}x{target_height}")
            depth = cv2.resize(depth, (target_width, target_height), interpolation=cv2.INTER_LINEAR)
            print(f"调整后深度尺寸: {depth.shape}")
    
    # 生成有效掩码
    valid_mask = (depth > -1000) & (depth < 1000) & (~np.isnan(depth)) & (~np.isinf(depth))
    
    # 深度统计
    if valid_mask.sum() > 0:
        valid_depth = depth[valid_mask]
        print(f"深度统计: min={valid_depth.min():.3f}, max={valid_depth.max():.3f}, mean={valid_depth.mean():.3f}")
        print(f"有效像素: {valid_mask.sum()}/{depth.size} ({100*valid_mask.sum()/depth.size:.1f}%)")
    
    return torch.from_numpy(depth).float(), torch.from_numpy(valid_mask).bool()

def compute_depth_by_monster(cameras):
    """为相机列表计算MonSter深度"""
    print("开始加载MonSter深度数据...")
    print(f"总共有 {len(cameras)} 个相机")
    
    # 先打印前几个相机的信息
    for i in range(min(3, len(cameras))):
        cam = cameras[i]
        print(f"相机 {i}: image_name='{cam.image_name}', 尺寸={cam.image_width}x{cam.image_height}")
    
    success_count = 0
    total_count = len(cameras)
    
    for i, cam in enumerate(cameras):
        print(f"\n=== 处理相机 {i+1}/{total_count} ===")
        print(f"Image name: {cam.image_name}")
        print(f"相机图像尺寸: {cam.image_width}x{cam.image_height}")
        
        try:
            # 查找对应的深度文件
            depth_file = find_monster_depth_file(cam.image_name)
            
            if depth_file and os.path.exists(depth_file):
                print(f"找到深度文件: {depth_file}")
                
                # 加载深度数据
                depth, depth_mask = load_monster_depth(
                    depth_file, 
                    target_height=cam.image_height,
                    target_width=cam.image_width
                )
                
                # 验证尺寸
                if depth.shape[0] == cam.image_height and depth.shape[1] == cam.image_width:
                    cam.set_monster_depth(depth, depth_mask)
                    success_count += 1
                    print(f"✅ MonSter深度加载成功")
                else:
                    print(f"❌ 尺寸不匹配: 深度{depth.shape} vs 图像{cam.image_height}x{cam.image_width}")
            else:
                print(f"❌ 未找到深度文件")
                
        except Exception as e:
            print(f"❌ 处理相机 {i} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"\n=== MonSter深度加载完成 ===")
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == 0:
        print("⚠️  警告: 没有成功加载任何深度数据!")
        return False
    elif success_count < total_count:
        print(f"⚠️  警告: 只成功加载了 {success_count}/{total_count} 个深度文件")
    
    return True
def find_monster_depth_file(image_name):
    """查找对应的MonSter深度文件"""
    # 从完整路径中提取基础文件名
    if '/' in image_name:
        base_name = os.path.basename(image_name)
    else:
        base_name = image_name
    
    # 移除扩展名
    base_name = os.path.splitext(base_name)[0]
    
    print(f"Looking for depth file for image: {image_name}")
    print(f"Base name: {base_name}")
    
    # 可能的深度文件路径
    depth_dirs = [
        "datasets/LLFF/scene/monster_stereo/depth_output",
        "datasets/LLFF/scene/4_views/depth",
        "datasets/LLFF/scene/depth_output"
    ]
    
    # 可能的文件名模式 - 根据实际文件名调整
    patterns = [
        f"{base_name}_L_depth.npy",  # image0001_L_depth.npy
        f"{base_name}_depth.npy",   # image0001_depth.npy
        f"{base_name}.npy",         # image0001.npy
        f"{base_name}_l_depth.npy"  # image0001_l_depth.npy
    ]
    
    # 搜索深度文件
    for depth_dir in depth_dirs:
        if os.path.exists(depth_dir):
            print(f"Searching in directory: {depth_dir}")
            for pattern in patterns:
                depth_file = os.path.join(depth_dir, pattern)
                print(f"  Checking: {depth_file}")
                if os.path.exists(depth_file):
                    print(f"✅ Found depth file: {depth_file}")
                    return depth_file
    
    # 如果没找到，列出目录内容帮助调试
    print("❌ No exact match found. Available files:")
    for depth_dir in depth_dirs:
        if os.path.exists(depth_dir):
            print(f"Files in {depth_dir}:")
            files = sorted(os.listdir(depth_dir))[:10]  # 显示前10个
            for f in files:
                print(f"  {f}")
            
            # 尝试模糊匹配
            for f in files:
                if base_name in f and f.endswith('.npy'):
                    fuzzy_match = os.path.join(depth_dir, f)
                    print(f"🔍 Fuzzy match found: {fuzzy_match}")
                    return fuzzy_match
            break
    
    print(f"❌ No depth file found for {image_name}")
    return None

def construct_pcd_from_monster(cameras):
    """从MonSter深度构建点云"""
    print("Constructing point cloud from MonSter depth...")
    
    all_points = []
    all_colors = []
    
    for i, cam in enumerate(cameras):
        if not hasattr(cam, 'monster_depth') or cam.monster_depth is None:
            continue
        
        points, colors = camera_to_points(cam)
        if points is not None:
            all_points.append(points)
            all_colors.append(colors)
            print(f"  Camera {i}: {len(points)} points")
    
    if len(all_points) == 0:
        raise RuntimeError("No valid points from MonSter depth")
    
    # 合并所有点
    final_points = np.concatenate(all_points, axis=0)
    final_colors = np.concatenate(all_colors, axis=0)
    
    print(f"Total points: {len(final_points)}")
    
    return BasicPointCloud(
        points=final_points, 
        colors=final_colors, 
        normals=np.zeros_like(final_points)
    )

def camera_to_points(cam):
    """将单个相机的深度转换为3D点"""
    depth = cam.monster_depth.squeeze()
    mask = cam.monster_depth_mask.squeeze()
    image = cam.original_image.permute(1, 2, 0)  # HWC
    
    H, W = depth.shape
    
    # 生成像素坐标网格
    u, v = torch.meshgrid(torch.arange(W), torch.arange(H), indexing='xy')
    u = u.cuda().float()
    v = v.cuda().float()
    
    # 应用掩码
    valid_pixels = mask & (depth > 0)
    if valid_pixels.sum() == 0:
        return None, None
    
    u_valid = u[valid_pixels]
    v_valid = v[valid_pixels]
    depth_valid = depth[valid_pixels]
    color_valid = image[valid_pixels]
    
    # 相机内参
    fx = 2 * cam.FoVx / np.pi  # 简化的焦距计算
    fy = 2 * cam.FoVy / np.pi
    cx = W / 2
    cy = H / 2
    
    # 像素坐标转相机坐标
    x_cam = (u_valid - cx) * depth_valid / fx
    y_cam = (v_valid - cy) * depth_valid / fy
    z_cam = depth_valid
    
    points_cam = torch.stack([x_cam, y_cam, z_cam], dim=1)
    
    # 相机坐标转世界坐标
    R = torch.tensor(cam.R, dtype=torch.float32).cuda()
    T = torch.tensor(cam.T, dtype=torch.float32).cuda().view(3, 1)
    
    points_world = (R.T @ (points_cam.T - T)).T
    
    return points_world.cpu().numpy(), color_valid.cpu().numpy()

def validate_depth_image_alignment(cameras):
    """验证深度图和图像的尺寸对齐"""
    print("验证深度图和图像尺寸对齐...")
    
    for i, cam in enumerate(cameras):
        if hasattr(cam, 'monster_depth') and cam.monster_depth is not None:
            img_h, img_w = cam.image_height, cam.image_width
            depth_shape = cam.monster_depth.shape
            
            print(f"相机 {i} ({cam.image_name}):")
            print(f"  图像尺寸: {img_w}x{img_h}")
            print(f"  深度尺寸: {depth_shape}")
            
            if len(depth_shape) == 2:
                depth_h, depth_w = depth_shape
            else:
                depth_h, depth_w = depth_shape[:2]
            
            if depth_h != img_h or depth_w != img_w:
                print(f"  ❌ 尺寸不匹配!")
                return False
            else:
                print(f"  ✅ 尺寸匹配")
    
    return True
