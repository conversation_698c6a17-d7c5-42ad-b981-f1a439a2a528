[5000, 10000, 30000]
Optimizing output/llff/scene
Output folder: output/llff/scene [24/07 20:13:44]
Successfully loaded PLY with 1447 points [24/07 20:13:44]

Reading camera 1/10idex 0 [24/07 20:13:44]

Reading camera 2/10idex 1 [24/07 20:13:44]

Reading camera 3/10idex 2 [24/07 20:13:44]

Reading camera 4/10idex 3 [24/07 20:13:44]

Reading camera 5/10idex 4 [24/07 20:13:44]

Reading camera 6/10idex 5 [24/07 20:13:44]

Reading camera 7/10idex 6 [24/07 20:13:44]

Reading camera 8/10idex 7 [24/07 20:13:44]

Reading camera 9/10idex 8 [24/07 20:13:44]

Reading camera 10/10idex 9 [24/07 20:13:44]
 [24/07 20:13:44]
eval,有验证集 [24/07 20:13:44]
5.762255191802979 cameras_extent [24/07 20:13:44]
Loading Training Cameras [24/07 20:13:44]

0it [00:00, ?it/s]
1it [00:00,  5.32it/s]
3it [00:00,  8.78it/s]
4it [00:00,  8.94it/s]
Loading Test Cameras [24/07 20:13:44]

0it [00:00, ?it/s]
2it [00:00, 16.16it/s]
2it [00:00, 16.14it/s]
Loading Eval Cameras [24/07 20:13:45]

0it [00:00, ?it/s]
3it [00:00, 22.21it/s]
4it [00:00, 23.08it/s]
Running MonSter preprocessing for datasets/LLFF/scene [24/07 20:13:45]
MonSter depth already exists, skipping preprocessing [24/07 20:13:45]
Loading MonSter depth maps to cameras... [24/07 20:13:45]
Processing camera 0: image0002 [24/07 20:13:45]
Warning: No MonSter depth found for image0002 [24/07 20:13:45]
Processing camera 1: image0004 [24/07 20:13:45]
Warning: No MonSter depth found for image0004 [24/07 20:13:45]
Processing camera 2: image0007 [24/07 20:13:45]
Warning: No MonSter depth found for image0007 [24/07 20:13:45]
Processing camera 3: image0010 [24/07 20:13:45]
Warning: No MonSter depth found for image0010 [24/07 20:13:45]
MonSter depth loading: 0/4 successful [24/07 20:13:45]
Traceback (most recent call last):
  File "/root/autodl-tmp/1/new/NexusGS/train.py", line 339, in <module>
    training(lp.extract(args), op.extract(args), pp.extract(args), args)
  File "/root/autodl-tmp/1/new/NexusGS/train.py", line 75, in training
    scene = Scene(args, gaussians, shuffle=False)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/autodl-tmp/1/new/NexusGS/scene/__init__.py", line 98, in __init__
    self._init_with_monster_depth(args, resolution_scales, scene_info)
  File "/root/autodl-tmp/1/new/NexusGS/scene/__init__.py", line 111, in _init_with_monster_depth
    compute_depth_by_monster(self.train_cameras[resolution_scale])
  File "/root/autodl-tmp/1/new/NexusGS/utils/monster_utils.py", line 204, in compute_depth_by_monster
    raise RuntimeError("No MonSter depth data loaded successfully")
RuntimeError: No MonSter depth data loaded successfully
Looking for config file in output/llff/scene/cfg_args
Config file found: output/llff/scene/cfg_args
Rendering output/llff/scene
Loading trained model at iteration 10000 [24/07 20:13:50]
Successfully loaded PLY with 1447 points [24/07 20:13:50]

Reading camera 1/10idex 0 [24/07 20:13:50]

Reading camera 2/10idex 1 [24/07 20:13:50]

Reading camera 3/10idex 2 [24/07 20:13:50]

Reading camera 4/10idex 3 [24/07 20:13:50]

Reading camera 5/10idex 4 [24/07 20:13:50]

Reading camera 6/10idex 5 [24/07 20:13:50]

Reading camera 7/10idex 6 [24/07 20:13:50]

Reading camera 8/10idex 7 [24/07 20:13:50]

Reading camera 9/10idex 8 [24/07 20:13:50]

Reading camera 10/10idex 9 [24/07 20:13:50]
 [24/07 20:13:50]
eval,有验证集 [24/07 20:13:50]
5.762255191802979 cameras_extent [24/07 20:13:50]
Loading Training Cameras [24/07 20:13:50]

0it [00:00, ?it/s]
1it [00:00,  2.64it/s]
3it [00:00,  5.99it/s]
4it [00:00,  6.11it/s]
Loading Test Cameras [24/07 20:13:51]

0it [00:00, ?it/s]
2it [00:00, 15.96it/s]
2it [00:00, 15.94it/s]
Loading Eval Cameras [24/07 20:13:51]

0it [00:00, ?it/s]
3it [00:00, 22.38it/s]
4it [00:00, 23.62it/s]

Rendering progress:   0%|          | 0/4 [00:00<?, ?it/s]/root/autodl-tmp/1/new/NexusGS/render.py:120: MatplotlibDeprecationWarning: The get_cmap function was deprecated in Matplotlib 3.7 and will be removed in 3.11. Use ``matplotlib.colormaps[name]`` or ``matplotlib.colormaps.get_cmap()`` or ``pyplot.get_cmap()`` instead.
  depth_map = visualize_cmap(depth_est, np.ones_like(depth_est), cm.get_cmap('turbo'), curve_fn=depth_curve_fn).copy()

Rendering progress:  25%|██▌       | 1/4 [00:01<00:05,  1.72s/it]
Rendering progress:  50%|█████     | 2/4 [00:03<00:03,  1.60s/it]
Rendering progress:  75%|███████▌  | 3/4 [00:04<00:01,  1.51s/it]
Rendering progress: 100%|██████████| 4/4 [00:06<00:00,  1.58s/it]
Rendering progress: 100%|██████████| 4/4 [00:06<00:00,  1.58s/it]

Rendering progress:   0%|          | 0/2 [00:00<?, ?it/s]
Rendering progress:  50%|█████     | 1/2 [00:01<00:01,  1.74s/it]
Rendering progress: 100%|██████████| 2/2 [00:03<00:00,  1.71s/it]
Rendering progress: 100%|██████████| 2/2 [00:03<00:00,  1.71s/it]

Scene: output/llff/scene
Method: ours_10000

Metric evaluation progress:   0%|          | 0/2 [00:00<?, ?it/s]