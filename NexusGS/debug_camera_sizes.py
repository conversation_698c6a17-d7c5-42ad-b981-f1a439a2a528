#!/usr/bin/env python3

import sys
sys.path.append('.')

import torch
from scene import Scene
from scene.gaussian_model import GaussianModel
from arguments import ModelParams, PipelineParams, OptimizationParams
from argparse import ArgumentParser
import numpy as np
from PIL import Image
import os

def debug_camera_sizes():
    """调试相机尺寸配置"""
    
    # 正确的参数解析方式
    parser = ArgumentParser(description="Debug camera sizes")
    lp = ModelParams(parser)
    pp = PipelineParams(parser)
    op = OptimizationParams(parser)
    
    # 手动设置参数
    args = parser.parse_args([
        "-s", "datasets/LLFF/scene",
        "--depth_method", "monster"
    ])
    
    # 提取参数对象
    model_params = lp.extract(args)
    
    # 正确传递args对象给GaussianModel
    gaussians = GaussianModel(model_params)
    scene = Scene(model_params, gaussians)
    
    train_cams = scene.getTrainCameras()
    test_cams = scene.getTestCameras()
    
    print("=== 相机尺寸详细调试 ===")
    
    # 检查原始图像文件尺寸
    source_path = model_params.source_path
    images_dir = os.path.join(source_path, "images")
    if os.path.exists(images_dir):
        image_files = sorted([f for f in os.listdir(images_dir) 
                             if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
        if image_files:
            first_img = Image.open(os.path.join(images_dir, image_files[0]))
            print(f"原始图像文件尺寸: {first_img.size} (W×H)")
    
    # 检查训练相机
    print(f"\n=== 训练相机 (共{len(train_cams)}个) ===")
    for i, cam in enumerate(train_cams[:3]):  # 只显示前3个
        print(f"训练相机 {i}:")
        print(f"  image_name: {cam.image_name}")
        print(f"  配置尺寸: {cam.image_width}×{cam.image_height}")
        print(f"  original_image shape: {cam.original_image.shape}")
        
        # 检查是否有MonSter深度
        if hasattr(cam, 'monster_depth') and cam.monster_depth is not None:
            print(f"  monster_depth shape: {cam.monster_depth.shape}")
        else:
            print(f"  monster_depth: None")
        print()
    
    # 检查测试相机
    print(f"=== 测试相机 (共{len(test_cams)}个) ===")
    for i, cam in enumerate(test_cams):
        print(f"测试相机 {i}:")
        print(f"  image_name: {cam.image_name}")
        print(f"  配置尺寸: {cam.image_width}×{cam.image_height}")
        print(f"  original_image shape: {cam.original_image.shape}")
        
        # 检查是否有MonSter深度
        if hasattr(cam, 'monster_depth') and cam.monster_depth is not None:
            print(f"  monster_depth shape: {cam.monster_depth.shape}")
        else:
            print(f"  monster_depth: None")
        print()
    
    # 对比第一个训练和测试相机
    if train_cams and test_cams:
        train_cam = train_cams[0]
        test_cam = test_cams[0]
        
        print("=== 训练 vs 测试对比 ===")
        print(f"训练相机配置: {train_cam.image_width}×{train_cam.image_height}")
        print(f"测试相机配置: {test_cam.image_width}×{test_cam.image_height}")
        print(f"训练图像tensor: {train_cam.original_image.shape}")
        print(f"测试图像tensor: {test_cam.original_image.shape}")
        
        # 检查尺寸比例
        if (train_cam.image_width != test_cam.image_width or 
            train_cam.image_height != test_cam.image_height):
            print("⚠️  发现尺寸不匹配!")
            
            width_ratio = test_cam.image_width / train_cam.image_width
            height_ratio = test_cam.image_height / train_cam.image_height
            print(f"尺寸比例: width_ratio={width_ratio:.4f}, height_ratio={height_ratio:.4f}")
            
            if abs(width_ratio - 0.5) < 0.1 and abs(height_ratio - 0.5) < 0.1:
                print("🔍 测试相机尺寸约为训练相机的一半 - 这解释了四分之一显示问题!")

if __name__ == "__main__":
    debug_camera_sizes()